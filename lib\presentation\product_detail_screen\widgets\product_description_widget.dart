import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class ProductDescriptionWidget extends StatefulWidget {
  final Map<String, dynamic> product;

  const ProductDescriptionWidget({
    Key? key,
    required this.product,
  }) : super(key: key);

  @override
  State<ProductDescriptionWidget> createState() =>
      _ProductDescriptionWidgetState();
}

class _ProductDescriptionWidgetState extends State<ProductDescriptionWidget> {
  bool _isDescriptionExpanded = false;
  bool _isIngredientsExpanded = false;
  bool _isNutritionExpanded = false;
  bool _isStorageExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildExpandableSection(
            title: 'Description',
            content: widget.product['description'] as String,
            isExpanded: _isDescriptionExpanded,
            onToggle: () => setState(
                () => _isDescriptionExpanded = !_isDescriptionExpanded),
          ),
          SizedBox(height: 2.h),
          _buildExpandableSection(
            title: 'Ingredients',
            content: widget.product['ingredients'] as String,
            isExpanded: _isIngredientsExpanded,
            onToggle: () => setState(
                () => _isIngredientsExpanded = !_isIngredientsExpanded),
          ),
          SizedBox(height: 2.h),
          _buildExpandableSection(
            title: 'Nutritional Information',
            content: widget.product['nutrition'] as String,
            isExpanded: _isNutritionExpanded,
            onToggle: () =>
                setState(() => _isNutritionExpanded = !_isNutritionExpanded),
          ),
          SizedBox(height: 2.h),
          _buildExpandableSection(
            title: 'Storage Instructions',
            content: widget.product['storage'] as String,
            isExpanded: _isStorageExpanded,
            onToggle: () =>
                setState(() => _isStorageExpanded = !_isStorageExpanded),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableSection({
    required String title,
    required String content,
    required bool isExpanded,
    required VoidCallback onToggle,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(3.w),
      ),
      child: Column(
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onToggle,
              borderRadius: BorderRadius.circular(3.w),
              child: Container(
                padding: EdgeInsets.all(4.w),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style:
                            AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    CustomIconWidget(
                      iconName: isExpanded
                          ? 'keyboard_arrow_up'
                          : 'keyboard_arrow_down',
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                      size: 6.w,
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (isExpanded)
            Container(
              padding: EdgeInsets.fromLTRB(4.w, 0, 4.w, 4.w),
              child: Text(
                content,
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
