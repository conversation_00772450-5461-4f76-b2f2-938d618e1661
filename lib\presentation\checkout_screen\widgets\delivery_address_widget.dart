import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../checkout_screen.dart';

class DeliveryAddressWidget extends StatefulWidget {
  final DeliveryAddress? selectedAddress;
  final Function(DeliveryAddress) onAddressSelected;

  const DeliveryAddressWidget({
    Key? key,
    required this.selectedAddress,
    required this.onAddressSelected,
  }) : super(key: key);

  @override
  State<DeliveryAddressWidget> createState() => _DeliveryAddressWidgetState();
}

class _DeliveryAddressWidgetState extends State<DeliveryAddressWidget> {
  List<DeliveryAddress> _addresses = [];
  bool _isLoadingLocation = false;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  void _loadAddresses() {
    // Mock addresses - in real app, this would come from user's saved addresses
    _addresses = [
      const DeliveryAddress(
        id: '1',
        label: 'Home',
        fullAddress: 'Riverside Drive, Westlands, Nairobi',
        coordinates: '-1.2921, 36.8219',
        isDefault: true,
      ),
      const DeliveryAddress(
        id: '2',
        label: 'Office',
        fullAddress: 'Upper Hill Road, Nairobi CBD',
        coordinates: '-1.2884, 36.8233',
        isDefault: false,
      ),
      const DeliveryAddress(
        id: '3',
        label: 'Mom\'s Place',
        fullAddress: 'Karen Road, Karen, Nairobi',
        coordinates: '-1.3197, 36.7519',
        isDefault: false,
      ),
    ];

    // Auto-select default address
    final defaultAddress = _addresses.firstWhere(
      (addr) => addr.isDefault,
      orElse: () => _addresses.first,
    );

    if (widget.selectedAddress == null) {
      widget.onAddressSelected(defaultAddress);
    }
  }

  void _useCurrentLocation() async {
    setState(() => _isLoadingLocation = true);

    // Simulate getting current location
    await Future.delayed(const Duration(seconds: 2));

    final currentLocationAddress = const DeliveryAddress(
      id: 'current',
      label: 'Current Location',
      fullAddress: 'Kilimani Road, Kilimani, Nairobi (Auto-detected)',
      coordinates: '-1.2979, 36.7884',
      isDefault: false,
    );

    widget.onAddressSelected(currentLocationAddress);

    setState(() => _isLoadingLocation = false);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Current location detected successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _addNewAddress() {
    // In real app, this would open address input form
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 70.h,
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 10.w,
                  height: 0.5.h,
                  decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),

              SizedBox(height: 3.h),

              Text(
                'Add New Address',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),

              SizedBox(height: 2.h),

              const Text('This feature will be available soon.'),
              const Text(
                  'You can use the current location option or select from existing addresses.'),

              SizedBox(height: 3.h),

              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current location option
          Container(
            margin: EdgeInsets.only(bottom: 2.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withAlpha(13),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withAlpha(51),
              ),
            ),
            child: ListTile(
              contentPadding: EdgeInsets.all(3.w),
              leading: Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.my_location,
                  color: Theme.of(context).primaryColor,
                  size: 20.sp,
                ),
              ),
              title: Text(
                'Use Current Location',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
              ),
              subtitle: const Text('We\'ll automatically detect your location'),
              trailing: _isLoadingLocation
                  ? SizedBox(
                      width: 5.w,
                      height: 5.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Theme.of(context).primaryColor,
                      ),
                    )
                  : Icon(
                      Icons.arrow_forward_ios,
                      color: Theme.of(context).primaryColor,
                      size: 16.sp,
                    ),
              onTap: _isLoadingLocation ? null : _useCurrentLocation,
            ),
          ),

          // Saved addresses section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Saved Addresses',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              TextButton.icon(
                onPressed: _addNewAddress,
                icon: Icon(
                  Icons.add,
                  size: 16.sp,
                ),
                label: const Text('Add New'),
              ),
            ],
          ),

          SizedBox(height: 1.h),

          // Addresses list
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _addresses.length,
            itemBuilder: (context, index) {
              final address = _addresses[index];
              final isSelected = widget.selectedAddress?.id == address.id;

              return Container(
                margin: EdgeInsets.only(bottom: 2.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).dividerColor,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: ListTile(
                  contentPadding: EdgeInsets.all(3.w),
                  leading: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor.withAlpha(26)
                          : Colors.grey.withAlpha(26),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      address.label == 'Home'
                          ? Icons.home_outlined
                          : address.label == 'Office'
                              ? Icons.business_outlined
                              : Icons.location_on_outlined,
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).textTheme.bodyMedium!.color,
                      size: 20.sp,
                    ),
                  ),
                  title: Row(
                    children: [
                      Text(
                        address.label,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? Theme.of(context).primaryColor
                                      : null,
                                ),
                      ),
                      if (address.isDefault) ...[
                        SizedBox(width: 2.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 2.w,
                            vertical: 0.5.h,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withAlpha(26),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Default',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 0.5.h),
                      Text(address.fullAddress),
                      SizedBox(height: 0.5.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 2.w,
                          vertical: 1.h,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.map_outlined,
                              color: Theme.of(context).primaryColor,
                              size: 14.sp,
                            ),
                            SizedBox(width: 2.w),
                            const Expanded(
                              child: Text('View on map'),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: Theme.of(context).primaryColor,
                          size: 20.sp,
                        )
                      : Icon(
                          Icons.radio_button_unchecked,
                          color: Theme.of(context).dividerColor,
                          size: 20.sp,
                        ),
                  onTap: () => widget.onAddressSelected(address),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
