# ChuiMart - E-commerce Flutter App

A modern Flutter-based e-commerce mobile application with PostgreSQL database integration for grocery and retail shopping.

## 📋 Prerequisites

- Flutter SDK (^3.6.0)
- Dart SDK
- Android Studio / VS Code with Flutter extensions
- Android SDK / Xcode (for iOS development)
- PostgreSQL database (Neon.tech or local)

## 🗄️ Database Setup

1. **PostgreSQL Database**: The app uses the provided Neon.tech PostgreSQL database:
   ```
   postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
   ```

2. **Run Database Schema**: Execute the `database_schema.sql` file to create the required tables:
   ```sql
   -- Connect to your PostgreSQL database and run:
   \i database_schema.sql
   ```

3. **Environment Configuration**: The database URL is already configured in `env.json`

## 🛠️ Installation

1. Install dependencies:
```bash
flutter pub get
```

2. Run the application:
```bash
flutter run
```

## 🏗️ Architecture & Features

### Database Integration
- **PostgreSQL**: Full database integration with Neon.tech
- **Real-time data**: Products, categories, users, orders, and cart management
- **Authentication**: User registration and login with database validation
- **No AI Dependencies**: Removed all OpenAI, Gemini, Anthropic, and Perplexity integrations

### Key Features
- **User Authentication**: Register, login, profile management
- **Product Catalog**: Browse categories, search products, view details
- **Shopping Cart**: Add/remove items, quantity management
- **Order Management**: Place orders, track delivery status
- **Responsive Design**: Optimized for all screen sizes

## 📁 Project Structure

```
chuimart/
├── android/                    # Android-specific configuration
├── ios/                       # iOS-specific configuration
├── lib/
│   ├── core/                  # Core business logic
│   │   ├── models/           # Data models (User, Product, Order, etc.)
│   │   ├── repositories/     # Database access layer
│   │   ├── services/         # Business services (Auth, Database)
│   │   └── utils/           # Utility classes and helpers
│   ├── presentation/         # UI screens and widgets
│   │   ├── home_screen/     # Main dashboard
│   │   ├── login_screen/    # Authentication
│   │   ├── product_*/       # Product-related screens
│   │   └── ...              # Other screens
│   ├── routes/              # Application routing
│   ├── theme/               # Theme configuration
│   ├── widgets/             # Reusable UI components
│   └── main.dart            # Application entry point
├── assets/                  # Static assets (images, etc.)
├── database_schema.sql      # PostgreSQL database schema
├── env.json                # Environment configuration
├── pubspec.yaml            # Project dependencies
└── README.md               # Project documentation
```

## 🧩 Adding Routes

To add new routes to the application, update the `lib/routes/app_routes.dart` file:

```dart
import 'package:flutter/material.dart';
import 'package:package_name/presentation/home_screen/home_screen.dart';

class AppRoutes {
  static const String initial = '/';
  static const String home = '/home';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    home: (context) => const HomeScreen(),
    // Add more routes as needed
  }
}
```

## 🎨 Theming

This project includes a comprehensive theming system with both light and dark themes:

```dart
// Access the current theme
ThemeData theme = Theme.of(context);

// Use theme colors
Color primaryColor = theme.colorScheme.primary;
```

The theme configuration includes:
- Color schemes for light and dark modes
- Typography styles
- Button themes
- Input decoration themes
- Card and dialog themes

## 📱 Responsive Design

The app is built with responsive design using the Sizer package:

```dart
// Example of responsive sizing
Container(
  width: 50.w, // 50% of screen width
  height: 20.h, // 20% of screen height
  child: Text('Responsive Container'),
)
```
## 📦 Deployment

Build the application for production:

```bash
# For Android
flutter build apk --release

# For iOS
flutter build ios --release
```

## 🙏 Acknowledgments
- Built with [Rocket.new](https://rocket.new)
- Powered by [Flutter](https://flutter.dev) & [Dart](https://dart.dev)
- Styled with Material Design

Built with ❤️ on Rocket.new
