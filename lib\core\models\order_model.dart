enum OrderStatus {
  pending,
  confirmed,
  preparing,
  outForDelivery,
  delivered,
  cancelled,
}

class OrderItemModel {
  final String id;
  final String productId;
  final String productName;
  final String productImageUrl;
  final double price;
  final double quantity;
  final String unit;
  final double totalPrice;

  OrderItemModel({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productImageUrl,
    required this.price,
    required this.quantity,
    required this.unit,
    required this.totalPrice,
  });

  factory OrderItemModel.fromMap(Map<String, dynamic> map) {
    return OrderItemModel(
      id: map['id']?.toString() ?? '',
      productId: map['product_id']?.toString() ?? '',
      productName: map['product_name']?.toString() ?? '',
      productImageUrl: map['product_image_url']?.toString() ?? '',
      price: double.tryParse(map['price']?.toString() ?? '0') ?? 0.0,
      quantity: double.tryParse(map['quantity']?.toString() ?? '0') ?? 0.0,
      unit: map['unit']?.toString() ?? 'piece',
      totalPrice: double.tryParse(map['total_price']?.toString() ?? '0') ?? 0.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'product_image_url': productImageUrl,
      'price': price,
      'quantity': quantity,
      'unit': unit,
      'total_price': totalPrice,
    };
  }
}

class DeliveryAddressModel {
  final String id;
  final String label;
  final String fullAddress;
  final String? landmark;
  final double? latitude;
  final double? longitude;

  DeliveryAddressModel({
    required this.id,
    required this.label,
    required this.fullAddress,
    this.landmark,
    this.latitude,
    this.longitude,
  });

  factory DeliveryAddressModel.fromMap(Map<String, dynamic> map) {
    return DeliveryAddressModel(
      id: map['id']?.toString() ?? '',
      label: map['label']?.toString() ?? '',
      fullAddress: map['full_address']?.toString() ?? '',
      landmark: map['landmark']?.toString(),
      latitude: map['latitude'] != null 
          ? double.tryParse(map['latitude']?.toString() ?? '0') 
          : null,
      longitude: map['longitude'] != null 
          ? double.tryParse(map['longitude']?.toString() ?? '0') 
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'label': label,
      'full_address': fullAddress,
      'landmark': landmark,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class OrderModel {
  final String id;
  final String userId;
  final String orderNumber;
  final OrderStatus status;
  final List<OrderItemModel> items;
  final double subtotal;
  final double deliveryFee;
  final double tax;
  final double totalAmount;
  final DeliveryAddressModel deliveryAddress;
  final String paymentMethod;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final String? notes;
  final String? trackingInfo;

  OrderModel({
    required this.id,
    required this.userId,
    required this.orderNumber,
    required this.status,
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.tax,
    required this.totalAmount,
    required this.deliveryAddress,
    required this.paymentMethod,
    required this.orderDate,
    this.deliveryDate,
    this.notes,
    this.trackingInfo,
  });

  String get statusDisplayName {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  factory OrderModel.fromMap(Map<String, dynamic> map) {
    return OrderModel(
      id: map['id']?.toString() ?? '',
      userId: map['user_id']?.toString() ?? '',
      orderNumber: map['order_number']?.toString() ?? '',
      status: _parseOrderStatus(map['status']?.toString() ?? 'pending'),
      items: (map['items'] as List<dynamic>?)
          ?.map((item) => OrderItemModel.fromMap(item))
          .toList() ?? [],
      subtotal: double.tryParse(map['subtotal']?.toString() ?? '0') ?? 0.0,
      deliveryFee: double.tryParse(map['delivery_fee']?.toString() ?? '0') ?? 0.0,
      tax: double.tryParse(map['tax']?.toString() ?? '0') ?? 0.0,
      totalAmount: double.tryParse(map['total_amount']?.toString() ?? '0') ?? 0.0,
      deliveryAddress: DeliveryAddressModel.fromMap(map['delivery_address'] ?? {}),
      paymentMethod: map['payment_method']?.toString() ?? '',
      orderDate: DateTime.parse(map['order_date']?.toString() ?? DateTime.now().toIso8601String()),
      deliveryDate: map['delivery_date'] != null 
          ? DateTime.parse(map['delivery_date']?.toString() ?? DateTime.now().toIso8601String())
          : null,
      notes: map['notes']?.toString(),
      trackingInfo: map['tracking_info']?.toString(),
    );
  }

  static OrderStatus _parseOrderStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'confirmed':
        return OrderStatus.confirmed;
      case 'preparing':
        return OrderStatus.preparing;
      case 'out_for_delivery':
      case 'outfordelivery':
        return OrderStatus.outForDelivery;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'order_number': orderNumber,
      'status': status.name,
      'items': items.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'delivery_fee': deliveryFee,
      'tax': tax,
      'total_amount': totalAmount,
      'delivery_address': deliveryAddress.toMap(),
      'payment_method': paymentMethod,
      'order_date': orderDate.toIso8601String(),
      'delivery_date': deliveryDate?.toIso8601String(),
      'notes': notes,
      'tracking_info': trackingInfo,
    };
  }

  @override
  String toString() {
    return 'OrderModel(id: $id, orderNumber: $orderNumber, status: $statusDisplayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
