{"name": "pg-connection-string", "version": "2.9.1", "description": "Functions for dealing with a PostgresSQL connection string", "main": "./index.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./esm/index.mjs", "require": "./index.js", "default": "./index.js"}}, "scripts": {"test": "nyc --reporter=lcov mocha && npm run check-coverage", "check-coverage": "nyc check-coverage --statements 100 --branches 100 --lines 100 --functions 100"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-connection-string"}, "keywords": ["pg", "connection", "string", "parse"], "author": "<PERSON> <<EMAIL>> (http://iceddev.com/)", "license": "MIT", "bugs": {"url": "https://github.com/brianc/node-postgres/issues"}, "homepage": "https://github.com/brianc/node-postgres/tree/master/packages/pg-connection-string", "devDependencies": {"@types/pg": "^8.12.0", "chai": "^4.1.1", "coveralls": "^3.0.4", "istanbul": "^0.4.5", "mocha": "^10.5.2", "nyc": "^15", "tsx": "^4.19.4", "typescript": "^4.0.3"}, "files": ["index.js", "index.d.ts", "esm"], "gitHead": "cd877a57612a39335a97b593111710d26126279d"}