const { Client } = require('pg');
const fs = require('fs');

async function checkDatabase() {
    // Read environment configuration
    const envConfig = JSON.parse(fs.readFileSync('env.json', 'utf8'));
    
    // Create PostgreSQL client
    const client = new Client({
        connectionString: envConfig.DATABASE_URL,
        ssl: {
            rejectUnauthorized: false
        }
    });

    try {
        console.log('Connecting to database...');
        await client.connect();
        console.log('Connected successfully!\n');

        // Check all tables
        const tablesResult = await client.query(`
            SELECT table_name, 
                   (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
            FROM information_schema.tables t
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);
        
        console.log('=== DATABASE TABLES ===');
        tablesResult.rows.forEach(row => {
            console.log(`📋 ${row.table_name} (${row.column_count} columns)`);
        });
        
        // Check categories
        const categories = await client.query('SELECT COUNT(*) as count FROM categories');
        console.log(`\n📂 Categories: ${categories.rows[0].count} records`);
        
        // Check products
        const products = await client.query('SELECT COUNT(*) as count FROM products');
        console.log(`📦 Products: ${products.rows[0].count} records`);
        
        // Check users
        const users = await client.query('SELECT COUNT(*) as count FROM users');
        console.log(`👥 Users: ${users.rows[0].count} records`);
        
        // Check cart items
        const cartItems = await client.query('SELECT COUNT(*) as count FROM cart_items');
        console.log(`🛒 Cart Items: ${cartItems.rows[0].count} records`);
        
        // Check if inventory tables exist
        const inventoryTables = ['inventory_transactions', 'stock_alerts', 'inventory_snapshots'];
        console.log('\n=== INVENTORY MANAGEMENT TABLES ===');
        
        for (const tableName of inventoryTables) {
            try {
                const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
                console.log(`📊 ${tableName}: ${result.rows[0].count} records`);
            } catch (error) {
                console.log(`❌ ${tableName}: Table does not exist`);
            }
        }
        
        // Check user roles
        console.log('\n=== USER ROLES ===');
        try {
            const roles = await client.query('SELECT role, COUNT(*) as count FROM users GROUP BY role ORDER BY role');
            roles.rows.forEach(row => {
                console.log(`👤 ${row.role}: ${row.count} users`);
            });
        } catch (error) {
            console.log('❌ Could not check user roles');
        }

    } catch (error) {
        console.error('Error:', error.message);
        if (error.detail) {
            console.error('Detail:', error.detail);
        }
    } finally {
        await client.end();
        console.log('\nDatabase connection closed.');
    }
}

checkDatabase();
