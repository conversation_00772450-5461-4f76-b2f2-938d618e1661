enum AlertType {
  lowStock,
  outOfStock,
  overstock,
}

class StockAlertModel {
  final String id;
  final String productId;
  final AlertType alertType;
  final double? thresholdValue;
  final double currentStock;
  final bool isResolved;
  final DateTime? resolvedAt;
  final String? resolvedBy;
  final DateTime createdAt;

  StockAlertModel({
    required this.id,
    required this.productId,
    required this.alertType,
    this.thresholdValue,
    required this.currentStock,
    this.isResolved = false,
    this.resolvedAt,
    this.resolvedBy,
    required this.createdAt,
  });

  factory StockAlertModel.fromMap(Map<String, dynamic> map) {
    return StockAlertModel(
      id: map['id']?.toString() ?? '',
      productId: map['product_id']?.toString() ?? '',
      alertType: _parseAlertType(map['alert_type']?.toString()),
      thresholdValue: map['threshold_value'] != null 
          ? double.tryParse(map['threshold_value']?.toString() ?? '0') 
          : null,
      currentStock: double.tryParse(map['current_stock']?.toString() ?? '0') ?? 0.0,
      isResolved: map['is_resolved'] == true || map['is_resolved'] == 1,
      resolvedAt: map['resolved_at'] != null 
          ? DateTime.parse(map['resolved_at']?.toString() ?? DateTime.now().toIso8601String())
          : null,
      resolvedBy: map['resolved_by']?.toString(),
      createdAt: DateTime.parse(map['created_at']?.toString() ?? DateTime.now().toIso8601String()),
    );
  }

  static AlertType _parseAlertType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'low_stock':
        return AlertType.lowStock;
      case 'out_of_stock':
        return AlertType.outOfStock;
      case 'overstock':
        return AlertType.overstock;
      default:
        return AlertType.lowStock;
    }
  }

  String get alertTypeString {
    switch (alertType) {
      case AlertType.lowStock:
        return 'low_stock';
      case AlertType.outOfStock:
        return 'out_of_stock';
      case AlertType.overstock:
        return 'overstock';
    }
  }

  String get alertMessage {
    switch (alertType) {
      case AlertType.lowStock:
        return 'Low stock alert: Only ${currentStock.toStringAsFixed(1)} units remaining';
      case AlertType.outOfStock:
        return 'Out of stock: Product is currently unavailable';
      case AlertType.overstock:
        return 'Overstock alert: ${currentStock.toStringAsFixed(1)} units exceed maximum level';
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'alert_type': alertTypeString,
      'threshold_value': thresholdValue,
      'current_stock': currentStock,
      'is_resolved': isResolved,
      'resolved_at': resolvedAt?.toIso8601String(),
      'resolved_by': resolvedBy,
      'created_at': createdAt.toIso8601String(),
    };
  }

  StockAlertModel copyWith({
    String? id,
    String? productId,
    AlertType? alertType,
    double? thresholdValue,
    double? currentStock,
    bool? isResolved,
    DateTime? resolvedAt,
    String? resolvedBy,
    DateTime? createdAt,
  }) {
    return StockAlertModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      alertType: alertType ?? this.alertType,
      thresholdValue: thresholdValue ?? this.thresholdValue,
      currentStock: currentStock ?? this.currentStock,
      isResolved: isResolved ?? this.isResolved,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      resolvedBy: resolvedBy ?? this.resolvedBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'StockAlertModel(id: $id, productId: $productId, type: $alertType, resolved: $isResolved)';
  }
}
