import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../order_tracking_screen.dart';

class OrderStatusWidget extends StatefulWidget {
  final OrderStatus currentStatus;
  final DateTime orderDate;

  const OrderStatusWidget({
    Key? key,
    required this.currentStatus,
    required this.orderDate,
  }) : super(key: key);

  @override
  State<OrderStatusWidget> createState() => _OrderStatusWidgetState();
}

class _OrderStatusWidgetState extends State<OrderStatusWidget>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  final List<StatusStep> _statusSteps = [
    StatusStep(
      status: OrderStatus.confirmed,
      title: 'Order Confirmed',
      description: 'Your order has been placed successfully',
      icon: Icons.check_circle_outline,
    ),
    StatusStep(
      status: OrderStatus.preparing,
      title: 'Preparing Order',
      description: 'We are preparing your items',
      icon: Icons.restaurant_menu,
    ),
    StatusStep(
      status: OrderStatus.outForDelivery,
      title: 'Out for Delivery',
      description: 'Your order is on the way',
      icon: Icons.delivery_dining,
    ),
    StatusStep(
      status: OrderStatus.delivered,
      title: 'Delivered',
      description: 'Order delivered successfully',
      icon: Icons.check_circle,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      _statusSteps.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 800 + (index * 200)),
        vsync: this,
      ),
    );

    _animations =
        _controllers.map((controller) {
          return Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(parent: controller, curve: Curves.elasticOut),
          );
        }).toList();
  }

  void _startAnimations() {
    final currentIndex = _statusSteps.indexWhere(
      (step) => step.status == widget.currentStatus,
    );

    for (int i = 0; i <= currentIndex && i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Status',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 3.h),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _statusSteps.length,
            itemBuilder: (context, index) {
              final step = _statusSteps[index];
              final isActive = step.status == widget.currentStatus;
              final isCompleted =
                  _getStatusIndex(step.status) <=
                  _getStatusIndex(widget.currentStatus);
              final isLast = index == _statusSteps.length - 1;

              return AnimatedBuilder(
                animation: _animations[index],
                builder: (context, child) {
                  return Transform.scale(
                    scale: 0.8 + (0.2 * _animations[index].value),
                    alignment: Alignment.centerLeft,
                    child: Opacity(
                      opacity: 0.3 + (0.7 * _animations[index].value),
                      child: Row(
                        children: [
                          // Status icon and line
                          Column(
                            children: [
                              Container(
                                width: 12.w,
                                height: 12.w,
                                decoration: BoxDecoration(
                                  color:
                                      isCompleted
                                          ? isActive
                                              ? Theme.of(context).primaryColor
                                              : Colors.green
                                          : Theme.of(context).dividerColor,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 3,
                                  ),
                                  boxShadow:
                                      isActive
                                          ? [
                                            BoxShadow(
                                              color: Theme.of(
                                                context,
                                              ).primaryColor.withAlpha(77),
                                              blurRadius: 10,
                                              spreadRadius: 2,
                                            ),
                                          ]
                                          : null,
                                ),
                                child: Icon(
                                  step.icon,
                                  color:
                                      isCompleted
                                          ? Colors.white
                                          : Colors.grey[600],
                                  size: 18.sp,
                                ),
                              ),
                              if (!isLast)
                                Container(
                                  width: 2,
                                  height: 6.h,
                                  color:
                                      isCompleted
                                          ? Colors.green.withAlpha(128)
                                          : Theme.of(context).dividerColor,
                                ),
                            ],
                          ),
                          SizedBox(width: 4.w),

                          // Status content
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.only(
                                bottom: isLast ? 0 : 3.h,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        step.title,
                                        style: Theme.of(
                                          context,
                                        ).textTheme.titleMedium?.copyWith(
                                          fontWeight:
                                              isActive
                                                  ? FontWeight.bold
                                                  : FontWeight.w500,
                                          color:
                                              isCompleted
                                                  ? Theme.of(
                                                    context,
                                                  ).textTheme.titleMedium?.color
                                                  : Theme.of(
                                                    context,
                                                  ).textTheme.bodySmall?.color,
                                        ),
                                      ),
                                      if (isCompleted)
                                        Text(
                                          _getStatusTime(step.status),
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                    ],
                                  ),
                                  SizedBox(height: 0.5.h),
                                  Text(
                                    step.description,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.copyWith(
                                      color:
                                          isCompleted
                                              ? Theme.of(
                                                context,
                                              ).textTheme.bodyMedium?.color
                                              : Theme.of(
                                                context,
                                              ).textTheme.bodySmall?.color,
                                    ),
                                  ),
                                  if (isActive &&
                                      step.status ==
                                          OrderStatus.outForDelivery) ...[
                                    SizedBox(height: 1.h),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 3.w,
                                        vertical: 1.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Theme.of(
                                          context,
                                        ).primaryColor.withAlpha(26),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.location_on,
                                            color:
                                                Theme.of(context).primaryColor,
                                            size: 14.sp,
                                          ),
                                          SizedBox(width: 1.w),
                                          Text(
                                            'Track in real-time',
                                            style: Theme.of(
                                              context,
                                            ).textTheme.bodySmall?.copyWith(
                                              color:
                                                  Theme.of(
                                                    context,
                                                  ).primaryColor,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  int _getStatusIndex(OrderStatus status) {
    return _statusSteps.indexWhere((step) => step.status == status);
  }

  String _getStatusTime(OrderStatus status) {
    final now = DateTime.now();
    switch (status) {
      case OrderStatus.confirmed:
        return _formatTime(widget.orderDate);
      case OrderStatus.preparing:
        return _formatTime(widget.orderDate.add(const Duration(minutes: 5)));
      case OrderStatus.outForDelivery:
        return _formatTime(now.subtract(const Duration(minutes: 10)));
      case OrderStatus.delivered:
        return widget.currentStatus == OrderStatus.delivered
            ? _formatTime(now)
            : '';
      case OrderStatus.cancelled:
        return _formatTime(now);
    }
  }

  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '$displayHour:$minute $period';
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }
}

class StatusStep {
  final OrderStatus status;
  final String title;
  final String description;
  final IconData icon;

  const StatusStep({
    required this.status,
    required this.title,
    required this.description,
    required this.icon,
  });
}