import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class StickyBottomBarWidget extends StatefulWidget {
  final Map<String, dynamic> product;
  final int quantity;
  final Function(int) onQuantityChanged;
  final VoidCallback onAddToCart;
  final VoidCallback onAddToWishlist;
  final VoidCallback onShare;

  const StickyBottomBarWidget({
    Key? key,
    required this.product,
    required this.quantity,
    required this.onQuantityChanged,
    required this.onAddToCart,
    required this.onAddToWishlist,
    required this.onShare,
  }) : super(key: key);

  @override
  State<StickyBottomBarWidget> createState() => _StickyBottomBarWidgetState();
}

class _StickyBottomBarWidgetState extends State<StickyBottomBarWidget> {
  bool _showQuickActions = false;

  @override
  Widget build(BuildContext context) {
    final bool isInStock = widget.product['inStock'] as bool;

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_showQuickActions)
                Container(
                  margin: EdgeInsets.only(bottom: 2.h),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: widget.onAddToWishlist,
                          icon: CustomIconWidget(
                            iconName: 'favorite_border',
                            color: AppTheme.lightTheme.primaryColor,
                            size: 5.w,
                          ),
                          label: Text('Wishlist'),
                        ),
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // Set reminder functionality
                          },
                          icon: CustomIconWidget(
                            iconName: 'notifications_none',
                            color: AppTheme.lightTheme.primaryColor,
                            size: 5.w,
                          ),
                          label: Text('Remind'),
                        ),
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: widget.onShare,
                          icon: CustomIconWidget(
                            iconName: 'share',
                            color: AppTheme.lightTheme.primaryColor,
                            size: 5.w,
                          ),
                          label: Text('Share'),
                        ),
                      ),
                    ],
                  ),
                ),
              Row(
                children: [
                  if (isInStock) ...[
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppTheme.lightTheme.colorScheme.outline,
                        ),
                        borderRadius: BorderRadius.circular(2.w),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: widget.quantity > 1
                                  ? () {
                                      widget.onQuantityChanged(
                                          widget.quantity - 1);
                                      HapticFeedback.lightImpact();
                                    }
                                  : null,
                              borderRadius: BorderRadius.circular(2.w),
                              child: Container(
                                padding: EdgeInsets.all(3.w),
                                child: CustomIconWidget(
                                  iconName: 'remove',
                                  color: widget.quantity > 1
                                      ? AppTheme
                                          .lightTheme.colorScheme.onSurface
                                      : AppTheme.lightTheme.colorScheme
                                          .onSurfaceVariant,
                                  size: 5.w,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 4.w, vertical: 3.w),
                            child: Text(
                              '${widget.quantity}',
                              style: AppTheme.lightTheme.textTheme.titleMedium
                                  ?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: widget.quantity < 10
                                  ? () {
                                      widget.onQuantityChanged(
                                          widget.quantity + 1);
                                      HapticFeedback.lightImpact();
                                    }
                                  : null,
                              borderRadius: BorderRadius.circular(2.w),
                              child: Container(
                                padding: EdgeInsets.all(3.w),
                                child: CustomIconWidget(
                                  iconName: 'add',
                                  color: widget.quantity < 10
                                      ? AppTheme
                                          .lightTheme.colorScheme.onSurface
                                      : AppTheme.lightTheme.colorScheme
                                          .onSurfaceVariant,
                                  size: 5.w,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 3.w),
                  ],
                  Expanded(
                    child: isInStock
                        ? GestureDetector(
                            onLongPress: () {
                              setState(() {
                                _showQuickActions = !_showQuickActions;
                              });
                              HapticFeedback.mediumImpact();
                            },
                            child: ElevatedButton(
                              onPressed: widget.onAddToCart,
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(vertical: 4.w),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomIconWidget(
                                    iconName: 'shopping_cart',
                                    color: Colors.white,
                                    size: 5.w,
                                  ),
                                  SizedBox(width: 2.w),
                                  Text(
                                    'Add to Cart',
                                    style: AppTheme
                                        .lightTheme.textTheme.titleMedium
                                        ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : ElevatedButton(
                            onPressed: () {
                              // Notify when available
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme
                                  .lightTheme.colorScheme.onSurfaceVariant,
                              padding: EdgeInsets.symmetric(vertical: 4.w),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomIconWidget(
                                  iconName: 'notifications_none',
                                  color: Colors.white,
                                  size: 5.w,
                                ),
                                SizedBox(width: 2.w),
                                Text(
                                  'Notify When Available',
                                  style: AppTheme
                                      .lightTheme.textTheme.titleMedium
                                      ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                ],
              ),
              if (!isInStock)
                Container(
                  margin: EdgeInsets.only(top: 2.h),
                  child: Text(
                    'Suggested alternatives available in similar products',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
