import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../checkout_screen.dart';

class CheckoutOrderSummaryWidget extends StatelessWidget {
  final Map<String, dynamic>? orderData;
  final DeliveryAddress? selectedAddress;
  final DeliveryTimeSlot? selectedTimeSlot;
  final PaymentMethod? selectedPaymentMethod;
  final String orderNotes;
  final Function(String) onOrderNotesChanged;

  const CheckoutOrderSummaryWidget({
    Key? key,
    required this.orderData,
    required this.selectedAddress,
    required this.selectedTimeSlot,
    required this.selectedPaymentMethod,
    required this.orderNotes,
    required this.onOrderNotesChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final subtotal = orderData?['subtotal']?.toDouble() ?? 0.0;
    final deliveryFee = orderData?['deliveryFee']?.toDouble() ?? 0.0;
    final taxes = orderData?['taxes']?.toDouble() ?? 0.0;
    final promoDiscount = orderData?['promoDiscount']?.toDouble() ?? 0.0;
    final total = orderData?['total']?.toDouble() ?? 0.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order review header
          Text(
            'Order Review',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),

          SizedBox(height: 3.h),

          // Delivery details summary
          _buildSummaryCard(
            context,
            title: 'Delivery Details',
            icon: Icons.location_on,
            children: [
              _buildDetailRow(
                context,
                'Address',
                selectedAddress?.fullAddress ?? 'Not selected',
                Icons.home_outlined,
              ),
              SizedBox(height: 1.h),
              _buildDetailRow(
                context,
                'Time Slot',
                selectedTimeSlot?.displayTime ?? 'Not selected',
                Icons.schedule,
              ),
              if (selectedTimeSlot?.isRecommended == true) ...[
                SizedBox(height: 0.5.h),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                  decoration: BoxDecoration(
                    color: Colors.green.withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Free delivery - Recommended slot',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.green,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 2.h),

          // Payment details summary
          _buildSummaryCard(
            context,
            title: 'Payment Method',
            icon: Icons.payment,
            children: [
              _buildDetailRow(
                context,
                selectedPaymentMethod?.name ?? 'Not selected',
                selectedPaymentMethod?.type == 'card'
                    ? '•••• •••• •••• ${selectedPaymentMethod?.lastFourDigits ?? ''}'
                    : selectedPaymentMethod?.provider ?? '',
                selectedPaymentMethod?.icon ?? Icons.payment,
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Order notes
          _buildSummaryCard(
            context,
            title: 'Special Instructions',
            icon: Icons.note_add,
            children: [
              TextField(
                onChanged: onOrderNotesChanged,
                maxLines: 3,
                decoration: const InputDecoration(
                  hintText:
                      'Add any special delivery instructions here (optional)',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Order summary
          _buildSummaryCard(
            context,
            title: 'Order Summary',
            icon: Icons.receipt_long,
            children: [
              _buildSummaryRow(
                  context, 'Subtotal', 'KES ${subtotal.toStringAsFixed(0)}'),
              _buildSummaryRow(context, 'Delivery Fee',
                  'KES ${deliveryFee.toStringAsFixed(0)}'),
              _buildSummaryRow(
                  context, 'Taxes (VAT)', 'KES ${taxes.toStringAsFixed(0)}'),
              if (promoDiscount > 0)
                _buildSummaryRow(
                  context,
                  'Promo Discount',
                  '- KES ${promoDiscount.toStringAsFixed(0)}',
                  isDiscount: true,
                ),
              Divider(height: 3.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Amount',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  Text(
                    'KES ${total.toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 3.h),

          // Terms and conditions
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).dividerColor,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).primaryColor,
                      size: 16.sp,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      'Order Policies',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Text(
                  '• Orders can be cancelled up to 15 minutes after confirmation\n'
                  '• Fresh items are subject to availability and may be substituted\n'
                  '• Delivery fees may vary based on location and time\n'
                  '• By placing this order, you agree to our terms and conditions',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 18.sp,
              ),
              SizedBox(width: 3.w),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          ...children,
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Theme.of(context).textTheme.bodyMedium!.color?.withAlpha(153),
          size: 16.sp,
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String amount, {
    bool isDiscount = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.5.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            amount,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isDiscount
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).textTheme.bodyMedium!.color,
                ),
          ),
        ],
      ),
    );
  }
}
