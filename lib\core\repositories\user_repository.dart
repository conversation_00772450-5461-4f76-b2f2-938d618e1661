import '../models/user_model.dart';
import '../services/database_service.dart';
import '../services/api_service.dart';

class UserRepository {
  final DatabaseService _db = DatabaseService.instance;
  final ApiService _api = ApiService.instance;
  String? _currentToken;

  /// Register a new user using PostgREST API
  Future<UserModel> registerUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      // Check if user already exists
      final existingUsers = await _api.get('/users', queryParameters: {
        'email': 'eq.$email',
        'select': 'id'
      });

      if (existingUsers['data'] != null && (existingUsers['data'] as List).isNotEmpty) {
        throw Exception('User with this email already exists');
      }

      // Create new user using PostgREST
      final userData = {
        'email': email,
        'password_hash': _hashPassword(password),
        'first_name': firstName,
        'last_name': lastName,
        'phone': phone,
      };

      final response = await _api.post('/users',
        data: userData,
        headers: {
          'Prefer': 'return=representation'
        }
      );

      if (response['data'] != null && (response['data'] as List).isNotEmpty) {
        final userData = (response['data'] as List).first;

        // Generate a simple session token
        _currentToken = 'session_${userData['id']}_${DateTime.now().millisecondsSinceEpoch}';
        _api.setAuthToken(_currentToken!);

        return UserModel.fromMap(userData);
      } else {
        throw Exception('Failed to create user');
      }
    } catch (e) {
      throw Exception('Registration failed: $e');
    }
  }

  /// Authenticate user login using PostgREST API
  Future<UserModel?> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      // Query user by email and password using PostgREST
      final response = await _api.get('/users', queryParameters: {
        'email': 'eq.$email',
        'password_hash': 'eq.${_hashPassword(password)}',
        'is_active': 'eq.true',
        'select': 'id,email,first_name,last_name,phone,profile_image_url,created_at,updated_at,is_active'
      });

      if (response['data'] != null && (response['data'] as List).isNotEmpty) {
        final userData = (response['data'] as List).first;
        final user = UserModel.fromMap(userData);

        // Generate a simple session token
        _currentToken = 'session_${user.id}_${DateTime.now().millisecondsSinceEpoch}';
        _api.setAuthToken(_currentToken!);

        return user;
      } else {
        return null; // Invalid credentials
      }
    } catch (e) {
      throw Exception('Login failed: $e');
    }
  }

  /// Get user by email
  Future<UserModel?> getUserByEmail(String email) async {
    try {
      final response = await _api.get('/users', queryParameters: {
        'email': 'eq.$email',
        'select': 'id,email,first_name,last_name,phone,profile_image_url,created_at,updated_at,is_active'
      });

      if (response['data'] != null && (response['data'] as List).isNotEmpty) {
        final userData = (response['data'] as List).first;
        return UserModel.fromMap(userData);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  /// Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final response = await _api.get('/users', queryParameters: {
        'id': 'eq.$userId',
        'is_active': 'eq.true',
        'select': 'id,email,first_name,last_name,phone,profile_image_url,created_at,updated_at,is_active'
      });

      if (response['data'] != null && (response['data'] as List).isNotEmpty) {
        final userData = (response['data'] as List).first;
        return UserModel.fromMap(userData);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  /// Update user profile
  Future<UserModel> updateUser(UserModel user) async {
    try {
      final result = await _db.queryOne(
        '''
        UPDATE users 
        SET first_name = @firstName, 
            last_name = @lastName, 
            phone = @phone, 
            profile_image_url = @profileImageUrl,
            updated_at = NOW()
        WHERE id = @userId
        RETURNING id, email, first_name, last_name, phone, profile_image_url, created_at, updated_at, is_active
        ''',
        parameters: {
          'userId': user.id,
          'firstName': user.firstName,
          'lastName': user.lastName,
          'phone': user.phone,
          'profileImageUrl': user.profileImageUrl,
        },
      );

      if (result == null) {
        throw Exception('Failed to update user');
      }

      return UserModel.fromMap(result);
    } catch (e) {
      throw Exception('Update failed: $e');
    }
  }

  /// Change user password
  Future<bool> changePassword({
    required String userId,
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // Verify current password
      final user = await _db.queryOne(
        '''
        SELECT id FROM users 
        WHERE id = @userId AND password_hash = @currentPassword
        ''',
        parameters: {
          'userId': userId,
          'currentPassword': _hashPassword(currentPassword),
        },
      );

      if (user == null) {
        throw Exception('Current password is incorrect');
      }

      // Update password
      final affectedRows = await _db.execute(
        '''
        UPDATE users 
        SET password_hash = @newPassword, updated_at = NOW()
        WHERE id = @userId
        ''',
        parameters: {
          'userId': userId,
          'newPassword': _hashPassword(newPassword),
        },
      );

      return affectedRows > 0;
    } catch (e) {
      throw Exception('Password change failed: $e');
    }
  }

  /// Deactivate user account
  Future<bool> deactivateUser(String userId) async {
    try {
      final affectedRows = await _db.execute(
        '''
        UPDATE users 
        SET is_active = false, updated_at = NOW()
        WHERE id = @userId
        ''',
        parameters: {'userId': userId},
      );

      return affectedRows > 0;
    } catch (e) {
      throw Exception('Deactivation failed: $e');
    }
  }

  /// Get current authentication token
  String? get currentToken => _currentToken;

  /// Clear current token
  void clearToken() {
    _currentToken = null;
    _api.clearAuthToken();
  }

  /// Simple password hashing (use proper hashing in production)
  String _hashPassword(String password) {
    // In production, use bcrypt or similar
    return password; // Temporary - implement proper hashing
  }
}
