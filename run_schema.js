const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function runSchema() {
    // Read environment configuration
    const envConfig = JSON.parse(fs.readFileSync('env.json', 'utf8'));
    
    // Create PostgreSQL client
    const client = new Client({
        connectionString: envConfig.DATABASE_URL,
        ssl: {
            rejectUnauthorized: false
        }
    });

    try {
        console.log('Connecting to database...');
        await client.connect();
        console.log('Connected successfully!');

        // Read the schema file
        const schemaPath = path.join(__dirname, 'simple_schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        
        console.log('Checking if schema needs to be updated...');

        // Check if tables exist
        const tablesResult = await client.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
        `);

        console.log('Existing tables:');
        tablesResult.rows.forEach(row => {
            console.log(`- ${row.table_name}`);
        });

        if (tablesResult.rows.length === 0) {
            console.log('No tables found. Running schema...');
            await client.query(schema);
            console.log('Schema executed successfully!');
        } else {
            console.log('Tables already exist. Skipping schema creation.');
        }
        
        // Test the setup by querying categories
        const result = await client.query('SELECT * FROM categories ORDER BY sort_order');
        console.log('\nCategories created:');
        result.rows.forEach(row => {
            console.log(`- ${row.name}: ${row.description}`);
        });
        
        // Test products
        const products = await client.query('SELECT name, price, unit FROM products ORDER BY name');
        console.log('\nProducts created:');
        products.rows.forEach(row => {
            console.log(`- ${row.name}: KES ${row.price}/${row.unit}`);
        });

    } catch (error) {
        console.error('Error:', error.message);
        if (error.detail) {
            console.error('Detail:', error.detail);
        }
    } finally {
        await client.end();
        console.log('Database connection closed.');
    }
}

runSchema();
