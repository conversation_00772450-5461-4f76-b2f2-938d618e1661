import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CategoryGridWidget extends StatelessWidget {
  const CategoryGridWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> categories = [
      {
        "id": 1,
        "name": "Vegetables",
        "icon": "eco",
        "color": "0xFF4CAF50",
      },
      {
        "id": 2,
        "name": "Fruits",
        "icon": "apple",
        "color": "0xFFFF9800",
      },
      {
        "id": 3,
        "name": "Dairy",
        "icon": "local_drink",
        "color": "0xFF2196F3",
      },
      {
        "id": 4,
        "name": "Meat",
        "icon": "restaurant",
        "color": "0xFFE91E63",
      },
      {
        "id": 5,
        "name": "<PERSON>y",
        "icon": "cake",
        "color": "0xFF9C27B0",
      },
      {
        "id": 6,
        "name": "Beverages",
        "icon": "local_cafe",
        "color": "0xFF607D8B",
      },
      {
        "id": 7,
        "name": "Snacks",
        "icon": "fastfood",
        "color": "0xFFFF5722",
      },
      {
        "id": 8,
        "name": "Household",
        "icon": "home",
        "color": "0xFF795548",
      },
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Shop by Category',
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/product-categories-screen');
                },
                child: Text(
                  'View All',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 3.w,
              mainAxisSpacing: 2.h,
              childAspectRatio: 0.8,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, '/product-categories-screen');
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.lightTheme.colorScheme.outline
                          .withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 12.w,
                        height: 6.h,
                        decoration: BoxDecoration(
                          color: Color(int.parse(category["color"] as String))
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: CustomIconWidget(
                            iconName: category["icon"] as String,
                            color:
                                Color(int.parse(category["color"] as String)),
                            size: 24,
                          ),
                        ),
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        category["name"] as String,
                        style:
                            AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
