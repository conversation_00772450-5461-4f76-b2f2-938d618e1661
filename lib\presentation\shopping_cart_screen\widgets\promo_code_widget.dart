import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class PromoCodeWidget extends StatefulWidget {
  final Function(String, double) onPromoApplied;
  final String currentPromo;

  const PromoCodeWidget({
    Key? key,
    required this.onPromoApplied,
    required this.currentPromo,
  }) : super(key: key);

  @override
  State<PromoCodeWidget> createState() => _PromoCodeWidgetState();
}

class _PromoCodeWidgetState extends State<PromoCodeWidget>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  bool _isApplying = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  final TextEditingController _promoController = TextEditingController();
  final FocusNode _promoFocusNode = FocusNode();

  // Mock promo codes
  final Map<String, double> _promoCodes = {
    'FIRST10': 50.0, // KES 50 off
    'SAVE100': 100.0, // KES 100 off
    'WELCOME': 75.0, // KES 75 off
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.currentPromo.isNotEmpty) {
      _promoController.text = widget.currentPromo;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _promoController.dispose();
    _promoFocusNode.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
        // Focus on text field when expanded
        Future.delayed(const Duration(milliseconds: 150), () {
          _promoFocusNode.requestFocus();
        });
      } else {
        _animationController.reverse();
        _promoFocusNode.unfocus();
      }
    });
  }

  void _applyPromoCode() async {
    final promoCode = _promoController.text.trim().toUpperCase();

    if (promoCode.isEmpty) return;

    setState(() => _isApplying = true);

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 800));

    if (_promoCodes.containsKey(promoCode)) {
      // Success
      widget.onPromoApplied(promoCode, _promoCodes[promoCode]!);

      // Show success animation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20.sp,
              ),
              SizedBox(width: 2.w),
              Text(
                  'Promo code applied! You saved KES ${_promoCodes[promoCode]!.toStringAsFixed(0)}'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );

      _toggleExpansion();
    } else {
      // Error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.white,
                size: 20.sp,
              ),
              SizedBox(width: 2.w),
              const Text('Invalid promo code. Please try again.'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    setState(() => _isApplying = false);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.currentPromo.isNotEmpty
              ? Theme.of(context).primaryColor.withAlpha(77)
              : Theme.of(context).dividerColor,
        ),
      ),
      child: Column(
        children: [
          // Promo code header
          InkWell(
            onTap: _toggleExpansion,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(4.w),
              child: Row(
                children: [
                  Icon(
                    Icons.local_offer_outlined,
                    color: Theme.of(context).primaryColor,
                    size: 20.sp,
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.currentPromo.isNotEmpty
                              ? 'Promo Applied: ${widget.currentPromo}'
                              : 'Have a promo code?',
                          style:
                              Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: widget.currentPromo.isNotEmpty
                                        ? Theme.of(context).primaryColor
                                        : null,
                                  ),
                        ),
                        if (widget.currentPromo.isEmpty)
                          Text(
                            'Tap to enter your code',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                      ],
                    ),
                  ),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Theme.of(context).primaryColor,
                      size: 20.sp,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Expandable promo code input
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Container(
              padding: EdgeInsets.only(
                left: 4.w,
                right: 4.w,
                bottom: 4.w,
              ),
              child: Column(
                children: [
                  Divider(
                    height: 1,
                    color: Theme.of(context).dividerColor,
                  ),
                  SizedBox(height: 3.w),

                  // Promo code input field
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _promoController,
                          focusNode: _promoFocusNode,
                          textCapitalization: TextCapitalization.characters,
                          decoration: InputDecoration(
                            hintText: 'Enter promo code',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 3.w,
                              vertical: 2.w,
                            ),
                            suffixIcon: _isApplying
                                ? Padding(
                                    padding: EdgeInsets.all(3.w),
                                    child: SizedBox(
                                      width: 4.w,
                                      height: 4.w,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  )
                                : null,
                          ),
                          enabled: !_isApplying,
                          onSubmitted: (_) => _applyPromoCode(),
                        ),
                      ),
                      SizedBox(width: 3.w),
                      ElevatedButton(
                        onPressed: _isApplying ? null : _applyPromoCode,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: 4.w,
                            vertical: 2.w,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Apply',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                        ),
                      ),
                    ],
                  ),

                  // Sample promo codes hint
                  SizedBox(height: 2.w),
                  Container(
                    padding: EdgeInsets.all(3.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withAlpha(13),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).primaryColor,
                          size: 16.sp,
                        ),
                        SizedBox(width: 2.w),
                        Expanded(
                          child: Text(
                            'Try: FIRST10, SAVE100, or WELCOME',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
