# ChuiMart Setup Instructions

## 🚀 Quick Start Guide

### 1. API and Database Setup

The app is configured to use your Neon Auth REST API and PostgreSQL database. The configuration is in `env.json`:

**REST API Endpoint:**
```
https://app-billowing-dew-50780600.dpl.myneon.app
```

**Database URL:**
```
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
```

### 2. Initialize Database Schema

**⚠️ IMPORTANT**: You MUST run the database schema before the API will work properly.

#### Option A: Using psql command line
```bash
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -f database_schema.sql
```

#### Option B: Using a PostgreSQL client (pgAdmin, DBeaver, etc.)
1. Connect to the database using the provided connection string
2. Open and execute the `database_schema.sql` file

#### Option C: Copy and paste the SQL
1. Open `database_schema.sql`
2. Copy all the SQL commands
3. Execute them in your preferred PostgreSQL client

#### Option D: Using Neon Console
1. Go to [Neon Console](https://console.neon.tech)
2. Navigate to your project
3. Open the SQL Editor
4. Copy and paste the contents of `database_schema.sql`
5. Execute the script

### 3. Install Flutter Dependencies

```bash
flutter pub get
```

### 4. Run the App

```bash
flutter run
```

### 5. Test API Integration

The app includes a built-in API testing screen to verify all endpoints are working:

1. **Launch the app**
2. **Navigate to Home Screen**
3. **Tap the bug icon** (🐛) in the top-right corner
4. **View API Test Results** - This will automatically test:
   - API connectivity
   - Database schema validation
   - User registration
   - User login
   - Categories endpoint
   - Products endpoint
   - Cart operations

The test screen will show you exactly which parts are working and which need attention.

## 🗄️ Database Schema Overview

The database includes the following main tables:

- **users**: User accounts and authentication
- **categories**: Product categories
- **products**: Product catalog
- **user_addresses**: User delivery addresses
- **orders**: Order information
- **order_items**: Individual items in orders
- **cart_items**: Shopping cart contents

## 🔧 Key Changes Made

### ✅ Removed AI Dependencies
- Removed OpenAI API integration
- Removed Gemini API integration
- Removed Anthropic API integration
- Removed Perplexity API integration

### ✅ Added REST API Integration
- **Neon Auth API**: User authentication via REST API endpoints
- **API Service**: HTTP client for API communication with proper error handling
- **Token Management**: JWT token handling and storage
- **Database Integration**: PostgreSQL for data persistence
- **Hybrid Architecture**: API for auth, database for data operations

### ✅ Updated Architecture
- Repository pattern for data access
- Service layer for business logic
- Model classes for data structures
- Authentication service for user sessions

## 🧪 Testing the App

### Sample Data
The database schema includes sample categories and products to get you started.

### Test User Accounts
You can create test user accounts through the registration screen, or manually insert test users into the database:

```sql
INSERT INTO users (email, password_hash, first_name, last_name, phone) 
VALUES ('<EMAIL>', 'password123', 'Test', 'User', '+**********');
```

**Note**: In production, passwords should be properly hashed. The current implementation uses plain text for simplicity.

## 🔒 Security Notes

### For Production Use:
1. **Password Hashing**: Implement proper password hashing (bcrypt, scrypt, etc.)
2. **JWT Tokens**: Replace simple session tokens with proper JWT implementation
3. **Input Validation**: Add comprehensive input validation and sanitization
4. **Rate Limiting**: Implement rate limiting for API endpoints
5. **SSL/TLS**: Ensure all connections use HTTPS
6. **Environment Variables**: Move sensitive configuration to environment variables

## 📱 App Features

### Current Features:
- User registration and login
- Product browsing by category
- Product search functionality
- Shopping cart management
- Order placement and tracking
- User profile management

### Database-Driven Features:
- Real-time product availability
- Dynamic pricing and discounts
- Order history
- User address management
- Category-based navigation

## 🛠️ Development Notes

### Database Connection:
- The app automatically initializes the database connection on startup
- Connection errors are handled gracefully with fallback to offline mode
- All database operations use parameterized queries to prevent SQL injection

### Error Handling:
- Comprehensive error handling for database operations
- User-friendly error messages
- Graceful degradation when database is unavailable

### Performance:
- Database queries are optimized with proper indexing
- Connection pooling is handled by the PostgreSQL driver
- Caching can be implemented using the existing SQLite integration
