import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class OrderSummaryWidget extends StatelessWidget {
  final double subtotal;
  final double deliveryFee;
  final double taxes;
  final double promoDiscount;
  final double total;
  final VoidCallback onCheckout;

  const OrderSummaryWidget({
    Key? key,
    required this.subtotal,
    required this.deliveryFee,
    required this.taxes,
    required this.promoDiscount,
    required this.total,
    required this.onCheckout,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Order summary header
              Row(
                children: [
                  Icon(
                    Icons.receipt_outlined,
                    color: Theme.of(context).primaryColor,
                    size: 20.sp,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    'Order Summary',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ],
              ),

              SizedBox(height: 2.h),

              // Order details
              Column(
                children: [
                  _buildSummaryRow(
                    context,
                    'Subtotal',
                    'KES ${subtotal.toStringAsFixed(0)}',
                  ),
                  _buildSummaryRow(
                    context,
                    'Delivery Fee',
                    'KES ${deliveryFee.toStringAsFixed(0)}',
                  ),
                  _buildSummaryRow(
                    context,
                    'Taxes (VAT)',
                    'KES ${taxes.toStringAsFixed(0)}',
                  ),
                  if (promoDiscount > 0)
                    _buildSummaryRow(
                      context,
                      'Promo Discount',
                      '- KES ${promoDiscount.toStringAsFixed(0)}',
                      isDiscount: true,
                    ),

                  Divider(
                    height: 3.h,
                    thickness: 1,
                    color: Theme.of(context).dividerColor,
                  ),

                  // Total
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      Text(
                        'KES ${total.toStringAsFixed(0)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 3.h),

              // Checkout button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onCheckout,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Proceed to Checkout',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      SizedBox(width: 2.w),
                      Icon(
                        Icons.arrow_forward,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 18.sp,
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 1.h),

              // Continue shopping button
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Continue Shopping',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String amount, {
    bool isDiscount = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.5.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            amount,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isDiscount
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).textTheme.bodyMedium!.color,
                ),
          ),
        ],
      ),
    );
  }
}
