import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

import './widgets/category_grid_widget.dart';
import './widgets/hero_banner_widget.dart';
import './widgets/location_selector_widget.dart';
import './widgets/popular_items_widget.dart';
import './widgets/recent_orders_widget.dart';
import './widgets/search_bar_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _currentBottomNavIndex = 0;
  String _currentLocation = "Juja, Kiambu County";
  final List<String> _savedLocations = [
    "Juja, Kiambu County",
    "JKUAT Main Campus",
    "Juja City Mall",
    "Kalimoni Estate",
  ];

  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  final AuthService _authService = AuthService.instance;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _onLocationChanged(String newLocation) {
    setState(() {
      _currentLocation = newLocation;
    });
    HapticFeedback.lightImpact();
  }

  void _onSearchChanged(String query) {
    // Handle search functionality
    if (query.isNotEmpty) {
      // Implement search logic
    }
  }

  void _onMicrophonePressed() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Voice search activated'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onQuickOrderPressed() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Barcode scanner activated'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onBottomNavTapped(int index) {
    setState(() {
      _currentBottomNavIndex = index;
    });
    HapticFeedback.selectionClick();

    switch (index) {
      case 0:
        // Home - already here
        break;
      case 1:
        Navigator.pushNamed(context, '/product-categories-screen');
        break;
      case 2:
        // Navigate to cart
        break;
      case 3:
        // Navigate to profile
        break;
    }
  }

  Future<void> _onRefresh() async {
    HapticFeedback.lightImpact();
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      // Refresh data
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _onRefresh,
          color: AppTheme.lightTheme.colorScheme.primary,
          child: CustomScrollView(
            slivers: [
              // Sticky Header
              SliverAppBar(
                floating: true,
                pinned: true,
                snap: false,
                elevation: 0,
                backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
                automaticallyImplyLeading: false,
                expandedHeight: 16.h,
                actions: [
                  // Manager Dashboard button (only show for managers)
                  if (_authService.currentUser?.canManageInventory == true)
                    IconButton(
                      icon: const Icon(Icons.dashboard, size: 20),
                      onPressed: () {
                        Navigator.pushNamed(context, AppRoutes.managerDashboard);
                      },
                      tooltip: 'Manager Dashboard',
                    ),
                  // Debug button for API testing
                  IconButton(
                    icon: const Icon(Icons.bug_report, size: 20),
                    onPressed: () {
                      Navigator.pushNamed(context, '/api-test-screen');
                    },
                    tooltip: 'API Test',
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                    child: Column(
                      children: [
                        // Location Selector
                        LocationSelectorWidget(
                          currentLocation: _currentLocation,
                          savedLocations: _savedLocations,
                          onLocationChanged: _onLocationChanged,
                        ),
                        SizedBox(height: 2.h),
                        // Search Bar
                        SearchBarWidget(
                          onSearchChanged: _onSearchChanged,
                          onMicrophonePressed: _onMicrophonePressed,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Main Content
              SliverList(
                delegate: SliverChildListDelegate([
                  SizedBox(height: 2.h),
                  // Hero Banner
                  const HeroBannerWidget(),
                  SizedBox(height: 4.h),
                  // Category Grid
                  const CategoryGridWidget(),
                  SizedBox(height: 4.h),
                  // Popular Items
                  const PopularItemsWidget(),
                  SizedBox(height: 4.h),
                  // Recent Orders
                  const RecentOrdersWidget(),
                  SizedBox(height: 10.h), // Bottom padding for FAB
                ]),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: AppTheme.lightTheme.colorScheme.shadow,
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentBottomNavIndex,
          onTap: _onBottomNavTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppTheme.lightTheme.colorScheme.surface,
          selectedItemColor: AppTheme.lightTheme.colorScheme.primary,
          unselectedItemColor:
              AppTheme.lightTheme.colorScheme.onSurface.withValues(alpha: 0.6),
          selectedLabelStyle:
              AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle:
              AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
            fontWeight: FontWeight.w400,
          ),
          items: [
            BottomNavigationBarItem(
              icon: CustomIconWidget(
                iconName: 'home',
                color: _currentBottomNavIndex == 0
                    ? AppTheme.lightTheme.colorScheme.primary
                    : AppTheme.lightTheme.colorScheme.onSurface
                        .withValues(alpha: 0.6),
                size: 24,
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: CustomIconWidget(
                iconName: 'category',
                color: _currentBottomNavIndex == 1
                    ? AppTheme.lightTheme.colorScheme.primary
                    : AppTheme.lightTheme.colorScheme.onSurface
                        .withValues(alpha: 0.6),
                size: 24,
              ),
              label: 'Categories',
            ),
            BottomNavigationBarItem(
              icon: Stack(
                children: [
                  CustomIconWidget(
                    iconName: 'shopping_cart',
                    color: _currentBottomNavIndex == 2
                        ? AppTheme.lightTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.onSurface
                            .withValues(alpha: 0.6),
                    size: 24,
                  ),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 4.w,
                      height: 2.h,
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.error,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          '3',
                          style: AppTheme.lightTheme.textTheme.labelSmall
                              ?.copyWith(
                            color: Colors.white,
                            fontSize: 8.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              label: 'Cart',
            ),
            BottomNavigationBarItem(
              icon: CustomIconWidget(
                iconName: 'person',
                color: _currentBottomNavIndex == 3
                    ? AppTheme.lightTheme.colorScheme.primary
                    : AppTheme.lightTheme.colorScheme.onSurface
                        .withValues(alpha: 0.6),
                size: 24,
              ),
              label: 'Profile',
            ),
          ],
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton.extended(
          onPressed: _onQuickOrderPressed,
          backgroundColor: AppTheme.lightTheme.colorScheme.secondary,
          foregroundColor: Colors.white,
          elevation: 4,
          icon: CustomIconWidget(
            iconName: 'qr_code_scanner',
            color: Colors.white,
            size: 24,
          ),
          label: Text(
            'Quick Order',
            style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
