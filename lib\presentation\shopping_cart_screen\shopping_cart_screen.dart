import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../routes/app_routes.dart';
import './widgets/cart_item_widget.dart';
import './widgets/empty_cart_widget.dart';
import './widgets/order_summary_widget.dart';
import './widgets/promo_code_widget.dart';

class ShoppingCartScreen extends StatefulWidget {
  const ShoppingCartScreen({Key? key}) : super(key: key);

  @override
  State<ShoppingCartScreen> createState() => _ShoppingCartScreenState();
}

class _ShoppingCartScreenState extends State<ShoppingCartScreen> {
  bool _isEditMode = false;

  List<CartItem> _cartItems = [];
  double _subtotal = 0.0;
  double _deliveryFee = 150.0; // KES 150 delivery fee
  double _taxes = 0.0;
  String _promoCode = '';
  double _promoDiscount = 0.0;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _loadCartItems();
  }

  void _loadCartItems() {
    // Mock cart data - in real app, this would come from a cart service/state management
    _cartItems = [
      CartItem(
        id: '1',
        name: 'Fresh Milk 1L',
        imageUrl:
            'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=400',
        price: 120.0,
        quantity: 2,
        unit: 'per bottle',
        isInStock: true,
        category: 'Dairy',
      ),
      CartItem(
        id: '2',
        name: 'White Bread Loaf',
        imageUrl:
            'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
        price: 85.0,
        quantity: 1,
        unit: 'per loaf',
        isInStock: true,
        category: 'Bakery',
      ),
      CartItem(
        id: '3',
        name: 'Bananas (Ripe)',
        imageUrl:
            'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
        price: 60.0,
        quantity: 3,
        unit: 'per kg',
        isInStock: false,
        category: 'Fruits',
      ),
    ];
    _calculateTotals();
  }

  void _calculateTotals() {
    _subtotal =
        _cartItems.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
    _taxes = _subtotal * 0.16; // 16% VAT
    setState(() {});
  }

  double get _total => _subtotal + _deliveryFee + _taxes - _promoDiscount;

  Future<void> _onRefresh() async {
    await Future.delayed(const Duration(seconds: 1));
    _loadCartItems();
  }

  void _updateQuantity(String itemId, int newQuantity) {
    setState(() {
      final index = _cartItems.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        if (newQuantity <= 0) {
          _cartItems.removeAt(index);
        } else {
          _cartItems[index] = _cartItems[index].copyWith(quantity: newQuantity);
        }
        _calculateTotals();
      }
    });
  }

  void _removeItem(String itemId) {
    setState(() {
      _cartItems.removeWhere((item) => item.id == itemId);
      _calculateTotals();
    });

    // Show undo snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Item removed from cart'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            // In real app, restore the item
            _loadCartItems();
          },
        ),
      ),
    );
  }

  void _applyPromoCode(String code, double discount) {
    setState(() {
      _promoCode = code;
      _promoDiscount = discount;
    });
  }

  void _proceedToCheckout() {
    if (_cartItems.isEmpty) return;

    Navigator.pushNamed(
      context,
      AppRoutes.checkoutScreen,
      arguments: {
        'cartItems': _cartItems,
        'subtotal': _subtotal,
        'deliveryFee': _deliveryFee,
        'taxes': _taxes,
        'promoDiscount': _promoDiscount,
        'total': _total,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Cart'),
        actions: [
          if (_cartItems.isNotEmpty)
            TextButton(
              onPressed: () {
                setState(() => _isEditMode = !_isEditMode);
              },
              child: Text(_isEditMode ? 'Done' : 'Edit'),
            ),
        ],
      ),
      body: SafeArea(
        child: _cartItems.isEmpty
            ? const EmptyCartWidget()
            : Column(
                children: [
                  Expanded(
                    child: RefreshIndicator(
                      key: _refreshIndicatorKey,
                      onRefresh: _onRefresh,
                      child: Column(
                        children: [
                          // Quick reorder section (if available)
                          Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: 4.w, vertical: 2.h),
                            padding: EdgeInsets.all(3.w),
                            decoration: BoxDecoration(
                              color: Theme.of(context).cardColor,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(13),
                                  blurRadius: 10,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.history,
                                  color: Theme.of(context).primaryColor,
                                  size: 20.sp,
                                ),
                                SizedBox(width: 3.w),
                                Expanded(
                                  child: Text(
                                    'Quick reorder from recent purchases',
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .color,
                                  size: 16.sp,
                                ),
                              ],
                            ),
                          ),

                          // Cart items list
                          Expanded(
                            child: ListView.builder(
                              padding: EdgeInsets.symmetric(horizontal: 4.w),
                              itemCount: _cartItems.length,
                              itemBuilder: (context, index) {
                                final item = _cartItems[index];
                                return CartItemWidget(
                                  item: item,
                                  isEditMode: _isEditMode,
                                  onQuantityChanged: _updateQuantity,
                                  onRemove: _removeItem,
                                );
                              },
                            ),
                          ),

                          // Promo code section
                          PromoCodeWidget(
                            onPromoApplied: _applyPromoCode,
                            currentPromo: _promoCode,
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Order summary and checkout button
                  OrderSummaryWidget(
                    subtotal: _subtotal,
                    deliveryFee: _deliveryFee,
                    taxes: _taxes,
                    promoDiscount: _promoDiscount,
                    total: _total,
                    onCheckout: _proceedToCheckout,
                  ),
                ],
              ),
      ),
    );
  }
}

// Cart item model
class CartItem {
  final String id;
  final String name;
  final String imageUrl;
  final double price;
  final int quantity;
  final String unit;
  final bool isInStock;
  final String category;

  const CartItem({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.price,
    required this.quantity,
    required this.unit,
    required this.isInStock,
    required this.category,
  });

  CartItem copyWith({
    String? id,
    String? name,
    String? imageUrl,
    double? price,
    int? quantity,
    String? unit,
    bool? isInStock,
    String? category,
  }) {
    return CartItem(
      id: id ?? this.id,
      name: name ?? this.name,
      imageUrl: imageUrl ?? this.imageUrl,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      isInStock: isInStock ?? this.isInStock,
      category: category ?? this.category,
    );
  }
}
