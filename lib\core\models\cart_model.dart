import 'product_model.dart';

class CartItemModel {
  final String id;
  final String userId;
  final ProductModel product;
  final double quantity;
  final DateTime createdAt;
  final DateTime updatedAt;

  CartItemModel({
    required this.id,
    required this.userId,
    required this.product,
    required this.quantity,
    required this.createdAt,
    required this.updatedAt,
  });

  double get totalPrice => product.finalPrice * quantity;

  factory CartItemModel.fromMap(Map<String, dynamic> map, ProductModel product) {
    return CartItemModel(
      id: map['id']?.toString() ?? '',
      userId: map['user_id']?.toString() ?? '',
      product: product,
      quantity: double.tryParse(map['quantity']?.toString() ?? '0') ?? 0.0,
      createdAt: DateTime.parse(map['created_at']?.toString() ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at']?.toString() ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'product_id': product.id,
      'quantity': quantity,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CartItemModel copyWith({
    String? id,
    String? userId,
    ProductModel? product,
    double? quantity,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CartItemModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CartItemModel(id: $id, product: ${product.name}, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class CartSummary {
  final List<CartItemModel> items;
  final double subtotal;
  final double deliveryFee;
  final double tax;
  final double total;
  final int totalItems;

  CartSummary({
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.tax,
    required this.total,
    required this.totalItems,
  });

  factory CartSummary.fromItems(List<CartItemModel> items, {
    double deliveryFee = 5.0,
    double taxRate = 0.08, // 8% tax
  }) {
    final subtotal = items.fold<double>(0, (sum, item) => sum + item.totalPrice);
    final tax = subtotal * taxRate;
    final total = subtotal + deliveryFee + tax;
    final totalItems = items.fold<int>(0, (sum, item) => sum + item.quantity.toInt());

    return CartSummary(
      items: items,
      subtotal: subtotal,
      deliveryFee: deliveryFee,
      tax: tax,
      total: total,
      totalItems: totalItems,
    );
  }

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
}
