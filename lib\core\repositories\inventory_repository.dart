import '../services/database_service.dart';
import '../models/inventory_transaction_model.dart';
import '../models/stock_alert_model.dart';
import '../models/product_model.dart';

class InventoryRepository {
  final DatabaseService _db = DatabaseService.instance;

  /// Update product stock and create transaction record
  Future<bool> updateProductStock({
    required String productId,
    required double newStock,
    required TransactionType transactionType,
    String? referenceId,
    String? notes,
    String? userId,
  }) async {
    try {
      // Get current stock
      final currentProduct = await _db.queryOne(
        'SELECT stock FROM products WHERE id = @productId',
        parameters: {'productId': productId},
      );

      if (currentProduct == null) {
        throw Exception('Product not found');
      }

      final previousStock = double.tryParse(currentProduct['stock']?.toString() ?? '0') ?? 0.0;
      final quantityChange = newStock - previousStock;

      // Start transaction
      await _db.execute('BEGIN');

      try {
        // Update product stock
        await _db.execute(
          '''
          UPDATE products 
          SET stock = @newStock, updated_at = NOW()
          WHERE id = @productId
          ''',
          parameters: {
            'productId': productId,
            'newStock': newStock,
          },
        );

        // Create inventory transaction record
        await _db.execute(
          '''
          INSERT INTO inventory_transactions 
          (product_id, transaction_type, quantity_change, previous_stock, new_stock, reference_id, notes, created_by)
          VALUES (@productId, @transactionType, @quantityChange, @previousStock, @newStock, @referenceId, @notes, @userId)
          ''',
          parameters: {
            'productId': productId,
            'transactionType': _getTransactionTypeString(transactionType),
            'quantityChange': quantityChange,
            'previousStock': previousStock,
            'newStock': newStock,
            'referenceId': referenceId,
            'notes': notes,
            'userId': userId,
          },
        );

        // Check for stock alerts
        await _checkAndCreateStockAlerts(productId, newStock);

        await _db.execute('COMMIT');
        return true;
      } catch (e) {
        await _db.execute('ROLLBACK');
        rethrow;
      }
    } catch (e) {
      throw Exception('Failed to update product stock: $e');
    }
  }

  /// Get inventory transactions for a product
  Future<List<InventoryTransactionModel>> getProductTransactions(
    String productId, {
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final results = await _db.query(
        '''
        SELECT * FROM inventory_transactions 
        WHERE product_id = @productId
        ORDER BY created_at DESC
        LIMIT @limit OFFSET @offset
        ''',
        parameters: {
          'productId': productId,
          'limit': limit,
          'offset': offset,
        },
      );

      return results.map((result) => InventoryTransactionModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get product transactions: $e');
    }
  }

  /// Get all inventory transactions
  Future<List<InventoryTransactionModel>> getAllTransactions({
    int limit = 100,
    int offset = 0,
    TransactionType? filterType,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      String whereClause = '';
      Map<String, dynamic> parameters = {
        'limit': limit,
        'offset': offset,
      };

      List<String> conditions = [];

      if (filterType != null) {
        conditions.add('transaction_type = @transactionType');
        parameters['transactionType'] = _getTransactionTypeString(filterType);
      }

      if (fromDate != null) {
        conditions.add('created_at >= @fromDate');
        parameters['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        conditions.add('created_at <= @toDate');
        parameters['toDate'] = toDate.toIso8601String();
      }

      if (conditions.isNotEmpty) {
        whereClause = 'WHERE ${conditions.join(' AND ')}';
      }

      final results = await _db.query(
        '''
        SELECT * FROM inventory_transactions 
        $whereClause
        ORDER BY created_at DESC
        LIMIT @limit OFFSET @offset
        ''',
        parameters: parameters,
      );

      return results.map((result) => InventoryTransactionModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get transactions: $e');
    }
  }

  /// Get active stock alerts
  Future<List<StockAlertModel>> getActiveStockAlerts() async {
    try {
      final results = await _db.query(
        '''
        SELECT * FROM stock_alerts 
        WHERE is_resolved = false
        ORDER BY created_at DESC
        ''',
      );

      return results.map((result) => StockAlertModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get stock alerts: $e');
    }
  }

  /// Resolve stock alert
  Future<bool> resolveStockAlert(String alertId, String userId) async {
    try {
      final affectedRows = await _db.execute(
        '''
        UPDATE stock_alerts 
        SET is_resolved = true, resolved_at = NOW(), resolved_by = @userId
        WHERE id = @alertId
        ''',
        parameters: {
          'alertId': alertId,
          'userId': userId,
        },
      );

      return affectedRows > 0;
    } catch (e) {
      throw Exception('Failed to resolve stock alert: $e');
    }
  }

  /// Get products with low stock
  Future<List<ProductModel>> getLowStockProducts() async {
    try {
      final results = await _db.query(
        '''
        SELECT p.*, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.stock <= p.min_stock_level AND p.is_available = true
        ORDER BY (p.stock / p.min_stock_level) ASC
        ''',
      );

      return results.map((result) => ProductModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get low stock products: $e');
    }
  }

  /// Check and create stock alerts for a product
  Future<void> _checkAndCreateStockAlerts(String productId, double currentStock) async {
    try {
      // Get product stock levels
      final product = await _db.queryOne(
        'SELECT min_stock_level, max_stock_level FROM products WHERE id = @productId',
        parameters: {'productId': productId},
      );

      if (product == null) return;

      final minStock = double.tryParse(product['min_stock_level']?.toString() ?? '0') ?? 0.0;
      final maxStock = double.tryParse(product['max_stock_level']?.toString() ?? '100') ?? 100.0;

      // Check for existing unresolved alerts for this product
      final existingAlert = await _db.queryOne(
        'SELECT id FROM stock_alerts WHERE product_id = @productId AND is_resolved = false',
        parameters: {'productId': productId},
      );

      // If there's an existing alert, don't create a new one
      if (existingAlert != null) return;

      AlertType? alertType;
      double? thresholdValue;

      if (currentStock <= 0) {
        alertType = AlertType.outOfStock;
        thresholdValue = 0;
      } else if (currentStock <= minStock) {
        alertType = AlertType.lowStock;
        thresholdValue = minStock;
      } else if (currentStock > maxStock) {
        alertType = AlertType.overstock;
        thresholdValue = maxStock;
      }

      if (alertType != null) {
        await _db.execute(
          '''
          INSERT INTO stock_alerts (product_id, alert_type, threshold_value, current_stock)
          VALUES (@productId, @alertType, @thresholdValue, @currentStock)
          ''',
          parameters: {
            'productId': productId,
            'alertType': _getAlertTypeString(alertType),
            'thresholdValue': thresholdValue,
            'currentStock': currentStock,
          },
        );
      }
    } catch (e) {
      // Don't throw here as this is a background operation
      print('Error creating stock alert: $e');
    }
  }

  String _getTransactionTypeString(TransactionType type) {
    switch (type) {
      case TransactionType.stockIn:
        return 'stock_in';
      case TransactionType.stockOut:
        return 'stock_out';
      case TransactionType.adjustment:
        return 'adjustment';
      case TransactionType.sale:
        return 'sale';
      case TransactionType.returnItem:
        return 'return';
    }
  }

  String _getAlertTypeString(AlertType type) {
    switch (type) {
      case AlertType.lowStock:
        return 'low_stock';
      case AlertType.outOfStock:
        return 'out_of_stock';
      case AlertType.overstock:
        return 'overstock';
    }
  }
}
