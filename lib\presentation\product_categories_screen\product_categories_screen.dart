import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../theme/app_theme.dart';
import './widgets/category_card_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/favorites_section_widget.dart';
import './widgets/filter_modal_widget.dart';
import './widgets/search_header_widget.dart';
import './widgets/section_header_widget.dart';

class ProductCategoriesScreen extends StatefulWidget {
  const ProductCategoriesScreen({Key? key}) : super(key: key);

  @override
  State<ProductCategoriesScreen> createState() =>
      _ProductCategoriesScreenState();
}

class _ProductCategoriesScreenState extends State<ProductCategoriesScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _allCategories = [];
  List<Map<String, dynamic>> _filteredCategories = [];
  List<Map<String, dynamic>> _favoriteCategories = [];
  Map<String, dynamic> _currentFilters = {};
  bool _isLoading = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadFavoriteCategories();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadCategories() {
    setState(() {
      _isLoading = true;
    });

    // Mock categories data
    _allCategories = [
      {
        'id': 1,
        'name': 'Fresh Fruits',
        'image':
            'https://images.pexels.com/photos/1132047/pexels-photo-1132047.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 45,
        'status': 'Available',
        'section': 'F',
      },
      {
        'id': 2,
        'name': 'Vegetables',
        'image':
            'https://images.pexels.com/photos/1300972/pexels-photo-1300972.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 38,
        'status': 'Available',
        'section': 'V',
      },
      {
        'id': 3,
        'name': 'Dairy Products',
        'image':
            'https://images.pexels.com/photos/236010/pexels-photo-236010.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 22,
        'status': 'Available',
        'section': 'D',
      },
      {
        'id': 4,
        'name': 'Meat & Poultry',
        'image':
            'https://images.pexels.com/photos/3688/food-dinner-lunch-chicken.jpg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 18,
        'status': 'Limited',
        'section': 'M',
      },
      {
        'id': 5,
        'name': 'Bakery Items',
        'image':
            'https://images.pexels.com/photos/209206/pexels-photo-209206.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 31,
        'status': 'Available',
        'section': 'B',
      },
      {
        'id': 6,
        'name': 'Beverages',
        'image':
            'https://images.pexels.com/photos/544961/pexels-photo-544961.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 56,
        'status': 'Available',
        'section': 'B',
      },
      {
        'id': 7,
        'name': 'Cereals & Grains',
        'image':
            'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 24,
        'status': 'Available',
        'section': 'C',
      },
      {
        'id': 8,
        'name': 'Snacks & Confectionery',
        'image':
            'https://images.pexels.com/photos/1191639/pexels-photo-1191639.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 67,
        'status': 'Available',
        'section': 'S',
      },
      {
        'id': 9,
        'name': 'Household Items',
        'image':
            'https://images.pexels.com/photos/4239091/pexels-photo-4239091.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 42,
        'status': 'Available',
        'section': 'H',
      },
      {
        'id': 10,
        'name': 'Personal Care',
        'image':
            'https://images.pexels.com/photos/3735657/pexels-photo-3735657.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 35,
        'status': 'Limited',
        'section': 'P',
      },
      {
        'id': 11,
        'name': 'Frozen Foods',
        'image':
            'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 19,
        'status': 'Available',
        'section': 'F',
      },
      {
        'id': 12,
        'name': 'Organic Products',
        'image':
            'https://images.pexels.com/photos/1300972/pexels-photo-1300972.jpeg?auto=compress&cs=tinysrgb&w=800',
        'itemCount': 28,
        'status': 'Available',
        'section': 'O',
      },
    ];

    _filteredCategories = List.from(_allCategories);

    setState(() {
      _isLoading = false;
    });
  }

  void _loadFavoriteCategories() {
    // Mock favorite categories (first 3 categories)
    _favoriteCategories = _allCategories.take(3).toList();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _applyFilters();
    });
  }

  void _applyFilters() {
    List<Map<String, dynamic>> filtered = List.from(_allCategories);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((category) {
        final name = (category['name'] as String).toLowerCase();
        return name.contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply other filters
    if (_currentFilters.isNotEmpty) {
      // Availability filter
      if (_currentFilters['available'] == true) {
        filtered = filtered
            .where((category) => category['status'] == 'Available')
            .toList();
      }
      if (_currentFilters['limited'] == true) {
        filtered = filtered
            .where((category) => category['status'] == 'Limited')
            .toList();
      }
    }

    // Sort alphabetically
    filtered
        .sort((a, b) => (a['name'] as String).compareTo(b['name'] as String));

    setState(() {
      _filteredCategories = filtered;
    });
  }

  void _onFiltersApplied(Map<String, dynamic> filters) {
    setState(() {
      _currentFilters = filters;
      _applyFilters();
    });
  }

  void _showFilterModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterModalWidget(
        currentFilters: _currentFilters,
        onFiltersApplied: _onFiltersApplied,
      ),
    );
  }

  void _onCategoryTap(Map<String, dynamic> category) {
    Navigator.pushNamed(context, '/product-detail-screen');
  }

  void _onRefresh() async {
    await Future.delayed(const Duration(seconds: 1));
    _loadCategories();
  }

  List<Widget> _buildCategoriesWithSections() {
    if (_filteredCategories.isEmpty) {
      return [
        EmptyStateWidget(
          title: 'No categories found',
          subtitle: _searchQuery.isNotEmpty
              ? 'Try adjusting your search or filters'
              : 'Pull to refresh and try again',
          suggestions: const ['Fruits', 'Vegetables', 'Dairy', 'Beverages'],
          onRetry: _onRefresh,
        ),
      ];
    }

    List<Widget> widgets = [];
    String currentSection = '';

    for (int i = 0; i < _filteredCategories.length; i++) {
      final category = _filteredCategories[i];
      final section = (category['section'] as String? ?? '').toUpperCase();

      // Add section header if it's a new section
      if (section != currentSection && section.isNotEmpty) {
        currentSection = section;
        widgets.add(
          SectionHeaderWidget(
            letter: section,
            isVisible: _searchQuery.isEmpty, // Hide sections when searching
          ),
        );
      }

      // Add category card
      widgets.add(
        CategoryCardWidget(
          category: category,
          onTap: () => _onCategoryTap(category),
        ),
      );
    }

    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Search Header
            SearchHeaderWidget(
              searchController: _searchController,
              onFilterTap: _showFilterModal,
              onSearchChanged: _onSearchChanged,
            ),

            // Main Content
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async => _onRefresh(),
                color: AppTheme.lightTheme.colorScheme.primary,
                child: _isLoading
                    ? Center(
                        child: CircularProgressIndicator(
                          color: AppTheme.lightTheme.colorScheme.primary,
                        ),
                      )
                    : CustomScrollView(
                        controller: _scrollController,
                        slivers: [
                          // Favorites Section (only show when not searching)
                          if (_searchQuery.isEmpty &&
                              _favoriteCategories.isNotEmpty)
                            SliverToBoxAdapter(
                              child: FavoritesSectionWidget(
                                favoriteCategories: _favoriteCategories,
                                onCategoryTap: _onCategoryTap,
                              ),
                            ),

                          // Categories List
                          SliverList(
                            delegate: SliverChildListDelegate(
                              _buildCategoriesWithSections(),
                            ),
                          ),

                          // Bottom Padding
                          SliverToBoxAdapter(
                            child: SizedBox(height: 10.h),
                          ),
                        ],
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
