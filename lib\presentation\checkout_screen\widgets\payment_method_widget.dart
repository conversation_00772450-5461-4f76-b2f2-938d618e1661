import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../checkout_screen.dart';

class PaymentMethodWidget extends StatefulWidget {
  final PaymentMethod? selectedPaymentMethod;
  final Function(PaymentMethod) onPaymentMethodSelected;

  const PaymentMethodWidget({
    Key? key,
    required this.selectedPaymentMethod,
    required this.onPaymentMethodSelected,
  }) : super(key: key);

  @override
  State<PaymentMethodWidget> createState() => _PaymentMethodWidgetState();
}

class _PaymentMethodWidgetState extends State<PaymentMethodWidget> {
  List<PaymentMethod> _paymentMethods = [];

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  void _loadPaymentMethods() {
    // Mock payment methods - in real app, this would come from user's saved payment methods
    _paymentMethods = [
      const PaymentMethod(
        id: '1',
        name: 'M-<PERSON><PERSON><PERSON>',
        type: 'mobile_money',
        provider: 'Safaricom',
        icon: Icons.phone_android,
        isDefault: true,
      ),
      const PaymentMethod(
        id: '2',
        name: 'Airtel Money',
        type: 'mobile_money',
        provider: 'Airtel',
        icon: Icons.phone_android,
        isDefault: false,
      ),
      const PaymentMethod(
        id: '3',
        name: 'Visa Card',
        type: 'card',
        lastFourDigits: '4532',
        provider: 'Visa',
        icon: Icons.credit_card,
        isDefault: false,
      ),
      const PaymentMethod(
        id: '4',
        name: 'Mastercard',
        type: 'card',
        lastFourDigits: '8901',
        provider: 'Mastercard',
        icon: Icons.credit_card,
        isDefault: false,
      ),
      const PaymentMethod(
        id: '5',
        name: 'Cash on Delivery',
        type: 'cod',
        icon: Icons.money,
        isDefault: false,
      ),
    ];

    // Auto-select default payment method
    if (widget.selectedPaymentMethod == null) {
      final defaultMethod = _paymentMethods.firstWhere(
        (method) => method.isDefault,
        orElse: () => _paymentMethods.first,
      );
      widget.onPaymentMethodSelected(defaultMethod);
    }
  }

  void _addNewCard() {
    // In real app, this would open card input form with secure tokenization
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 70.h,
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 10.w,
                  height: 0.5.h,
                  decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),

              SizedBox(height: 3.h),

              Text(
                'Add New Card',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),

              SizedBox(height: 2.h),

              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withAlpha(13),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withAlpha(51),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.security,
                          color: Theme.of(context).primaryColor,
                          size: 20.sp,
                        ),
                        SizedBox(width: 3.w),
                        Text(
                          'Secure Payment',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ],
                    ),
                    SizedBox(height: 1.h),
                    const Text(
                        'Your card information is encrypted and stored securely. We never store your CVV.'),
                  ],
                ),
              ),

              SizedBox(height: 3.h),

              const Text('Card addition feature will be available soon.'),
              const Text(
                  'For now, you can use Mobile Money or Cash on Delivery.'),

              SizedBox(height: 3.h),

              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getProviderColor(String? provider) {
    switch (provider) {
      case 'Safaricom':
        return Colors.green;
      case 'Airtel':
        return Colors.red;
      case 'Visa':
        return Colors.blue;
      case 'Mastercard':
        return Colors.orange;
      default:
        return Theme.of(context).primaryColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment methods section header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment Methods',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              TextButton.icon(
                onPressed: _addNewCard,
                icon: Icon(
                  Icons.add,
                  size: 16.sp,
                ),
                label: const Text('Add Card'),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Payment methods list
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _paymentMethods.length,
            itemBuilder: (context, index) {
              final paymentMethod = _paymentMethods[index];
              final isSelected =
                  widget.selectedPaymentMethod?.id == paymentMethod.id;

              return Container(
                margin: EdgeInsets.only(bottom: 2.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).dividerColor,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: ListTile(
                  contentPadding: EdgeInsets.all(3.w),
                  leading: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: _getProviderColor(paymentMethod.provider)
                          .withAlpha(26),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      paymentMethod.icon,
                      color: _getProviderColor(paymentMethod.provider),
                      size: 20.sp,
                    ),
                  ),
                  title: Row(
                    children: [
                      Text(
                        paymentMethod.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? Theme.of(context).primaryColor
                                      : null,
                                ),
                      ),
                      if (paymentMethod.isDefault) ...[
                        SizedBox(width: 2.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 2.w,
                            vertical: 0.5.h,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withAlpha(26),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Default',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 0.5.h),
                      if (paymentMethod.type == 'card')
                        Text('•••• •••• •••• ${paymentMethod.lastFourDigits}')
                      else if (paymentMethod.type == 'mobile_money')
                        Text('Pay with ${paymentMethod.provider}')
                      else
                        const Text('Pay cash when your order is delivered'),

                      SizedBox(height: 1.h),

                      // Payment method features
                      Container(
                        padding: EdgeInsets.all(2.w),
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            if (paymentMethod.type == 'mobile_money') ...[
                              Row(
                                children: [
                                  Icon(
                                    Icons.security,
                                    color: Colors.green,
                                    size: 14.sp,
                                  ),
                                  SizedBox(width: 2.w),
                                  const Text('Secure & Instant'),
                                  const Spacer(),
                                  Text(
                                    'No extra fees',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: Colors.green,
                                          fontWeight: FontWeight.w500,
                                        ),
                                  ),
                                ],
                              ),
                            ] else if (paymentMethod.type == 'card') ...[
                              Row(
                                children: [
                                  Icon(
                                    Icons.lock,
                                    color: Colors.blue,
                                    size: 14.sp,
                                  ),
                                  SizedBox(width: 2.w),
                                  const Text('Secure Payment'),
                                  const Spacer(),
                                  Text(
                                    '2% processing fee',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ] else ...[
                              Row(
                                children: [
                                  Icon(
                                    Icons.delivery_dining,
                                    color: Colors.orange,
                                    size: 14.sp,
                                  ),
                                  SizedBox(width: 2.w),
                                  const Text('Pay on Delivery'),
                                  const Spacer(),
                                  Text(
                                    'Exact change preferred',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                  trailing: Icon(
                    isSelected
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).dividerColor,
                    size: 20.sp,
                  ),
                  onTap: () => widget.onPaymentMethodSelected(paymentMethod),
                ),
              );
            },
          ),

          SizedBox(height: 2.h),

          // Security information
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withAlpha(13),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withAlpha(51),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.verified_user,
                      color: Theme.of(context).primaryColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      'Secure Payments',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Text(
                  'All payments are processed securely through our trusted payment partners. Your financial information is protected with bank-level encryption.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                ),
                SizedBox(height: 1.h),
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 14.sp,
                    ),
                    SizedBox(width: 1.w),
                    const Text('256-bit SSL encryption'),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 14.sp,
                    ),
                    SizedBox(width: 1.w),
                    const Text('PCI DSS compliant'),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
