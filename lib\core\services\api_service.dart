import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../utils/env_config.dart';

class ApiService {
  static ApiService? _instance;
  late Dio _dio;
  
  ApiService._internal() {
    _dio = Dio();
    _setupInterceptors();
  }
  
  static ApiService get instance {
    _instance ??= ApiService._internal();
    return _instance!;
  }
  
  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add common headers
          options.headers['Content-Type'] = 'application/json';
          options.headers['Accept'] = 'application/json';
          
          if (kDebugMode) {
            print('API Request: ${options.method} ${options.uri}');
            print('Headers: ${options.headers}');
            if (options.data != null) {
              print('Body: ${options.data}');
            }
          }
          
          handler.next(options);
        },
        onResponse: (response, handler) {
          if (kDebugMode) {
            print('API Response: ${response.statusCode} ${response.requestOptions.uri}');
            print('Data: ${response.data}');
          }
          handler.next(response);
        },
        onError: (error, handler) {
          if (kDebugMode) {
            print('API Error: ${error.message}');
            print('Response: ${error.response?.data}');
          }
          handler.next(error);
        },
      ),
    );
  }
  
  /// POST request
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.post(
        '${EnvConfig.restApiEndpoint}$endpoint',
        data: data,
        options: Options(headers: headers),
      );
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// GET request
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.get(
        '${EnvConfig.restApiEndpoint}$endpoint',
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// PUT request
  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.put(
        '${EnvConfig.restApiEndpoint}$endpoint',
        data: data,
        options: Options(headers: headers),
      );
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// DELETE request
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.delete(
        '${EnvConfig.restApiEndpoint}$endpoint',
        options: Options(headers: headers),
      );
      
      return _handleResponse(response);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Handle successful response
  Map<String, dynamic> _handleResponse(Response response) {
    if (response.statusCode! >= 200 && response.statusCode! < 300) {
      if (response.data is Map<String, dynamic>) {
        return response.data;
      } else if (response.data is List) {
        // PostgREST returns arrays directly
        return {'data': response.data, 'success': true};
      } else if (response.data is String) {
        try {
          final decoded = json.decode(response.data);
          if (decoded is List) {
            return {'data': decoded, 'success': true};
          }
          return decoded;
        } catch (e) {
          return {'message': response.data, 'success': true};
        }
      } else {
        return {'data': response.data, 'success': true};
      }
    } else {
      throw Exception('HTTP ${response.statusCode}: ${response.statusMessage}');
    }
  }
  
  /// Handle API errors
  Exception _handleError(DioException error) {
    String message = 'An error occurred';
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Request timeout. Please try again.';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Response timeout. Please try again.';
        break;
      case DioExceptionType.badResponse:
        if (error.response?.data != null) {
          if (error.response!.data is Map<String, dynamic>) {
            message = error.response!.data['message'] ?? 
                     error.response!.data['error'] ?? 
                     'Server error occurred';
          } else if (error.response!.data is String) {
            message = error.response!.data;
          }
        } else {
          message = 'HTTP ${error.response?.statusCode}: ${error.response?.statusMessage}';
        }
        break;
      case DioExceptionType.cancel:
        message = 'Request was cancelled';
        break;
      case DioExceptionType.connectionError:
        message = 'Connection error. Please check your internet connection.';
        break;
      case DioExceptionType.badCertificate:
        message = 'Certificate error. Please check your connection security.';
        break;
      case DioExceptionType.unknown:
        message = error.message ?? 'Unknown error occurred';
        break;
    }
    
    return Exception(message);
  }
  
  /// Set authorization header for authenticated requests
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }
  
  /// Clear authorization header
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }
}
