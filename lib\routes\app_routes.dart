import 'package:flutter/material.dart';
import '../presentation/product_detail_screen/product_detail_screen.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/home_screen/home_screen.dart';
import '../presentation/registration_screen/registration_screen.dart';
import '../presentation/product_categories_screen/product_categories_screen.dart';
import '../presentation/shopping_cart_screen/shopping_cart_screen.dart';
import '../presentation/checkout_screen/checkout_screen.dart';
import '../presentation/order_tracking_screen/order_tracking_screen.dart';
import '../presentation/user_profile_screen/user_profile_screen.dart';
import '../presentation/api_test_screen/api_test_screen.dart';
import '../presentation/manager_dashboard/manager_dashboard_screen.dart';
import '../presentation/manager_dashboard/stock_update_screen.dart';
import '../presentation/manager_dashboard/inventory_management_screen.dart';

class AppRoutes {
  static const String initial = '/';
  static const String productDetail = '/product-detail-screen';
  static const String splash = '/splash-screen';
  static const String login = '/login-screen';
  static const String home = '/home-screen';
  static const String registration = '/registration-screen';
  static const String productCategories = '/product-categories-screen';
  static const String shoppingCartScreen = '/shopping-cart-screen';
  static const String checkoutScreen = '/checkout-screen';
  static const String orderTracking = '/order-tracking-screen';
  static const String userProfile = '/user-profile-screen';
  static const String apiTest = '/api-test-screen';
  static const String managerDashboard = '/manager-dashboard-screen';
  static const String stockUpdate = '/stock-update-screen';
  static const String inventoryManagement = '/inventory-management-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    productDetail: (context) => const ProductDetailScreen(),
    splash: (context) => const SplashScreen(),
    login: (context) => const LoginScreen(),
    home: (context) => const HomeScreen(),
    registration: (context) => const RegistrationScreen(),
    productCategories: (context) => const ProductCategoriesScreen(),
    shoppingCartScreen: (context) => const ShoppingCartScreen(),
    checkoutScreen: (context) => const CheckoutScreen(),
    orderTracking: (context) => const OrderTrackingScreen(),
    userProfile: (context) => const UserProfileScreen(),
    apiTest: (context) => const ApiTestScreen(),
    managerDashboard: (context) => const ManagerDashboardScreen(),
    stockUpdate: (context) => const StockUpdateScreen(),
    inventoryManagement: (context) => const InventoryManagementScreen(),
  };
}
