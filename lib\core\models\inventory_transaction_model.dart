enum TransactionType {
  stockIn,
  stockOut,
  adjustment,
  sale,
  returnItem,
}

class InventoryTransactionModel {
  final String id;
  final String productId;
  final TransactionType transactionType;
  final double quantityChange;
  final double previousStock;
  final double newStock;
  final String? referenceId;
  final String? notes;
  final String? createdBy;
  final DateTime createdAt;

  InventoryTransactionModel({
    required this.id,
    required this.productId,
    required this.transactionType,
    required this.quantityChange,
    required this.previousStock,
    required this.newStock,
    this.referenceId,
    this.notes,
    this.createdBy,
    required this.createdAt,
  });

  factory InventoryTransactionModel.fromMap(Map<String, dynamic> map) {
    return InventoryTransactionModel(
      id: map['id']?.toString() ?? '',
      productId: map['product_id']?.toString() ?? '',
      transactionType: _parseTransactionType(map['transaction_type']?.toString()),
      quantityChange: double.tryParse(map['quantity_change']?.toString() ?? '0') ?? 0.0,
      previousStock: double.tryParse(map['previous_stock']?.toString() ?? '0') ?? 0.0,
      newStock: double.tryParse(map['new_stock']?.toString() ?? '0') ?? 0.0,
      referenceId: map['reference_id']?.toString(),
      notes: map['notes']?.toString(),
      createdBy: map['created_by']?.toString(),
      createdAt: DateTime.parse(map['created_at']?.toString() ?? DateTime.now().toIso8601String()),
    );
  }

  static TransactionType _parseTransactionType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'stock_in':
        return TransactionType.stockIn;
      case 'stock_out':
        return TransactionType.stockOut;
      case 'adjustment':
        return TransactionType.adjustment;
      case 'sale':
        return TransactionType.sale;
      case 'return':
        return TransactionType.returnItem;
      default:
        return TransactionType.adjustment;
    }
  }

  String get transactionTypeString {
    switch (transactionType) {
      case TransactionType.stockIn:
        return 'stock_in';
      case TransactionType.stockOut:
        return 'stock_out';
      case TransactionType.adjustment:
        return 'adjustment';
      case TransactionType.sale:
        return 'sale';
      case TransactionType.returnItem:
        return 'return';
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'transaction_type': transactionTypeString,
      'quantity_change': quantityChange,
      'previous_stock': previousStock,
      'new_stock': newStock,
      'reference_id': referenceId,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
    };
  }

  InventoryTransactionModel copyWith({
    String? id,
    String? productId,
    TransactionType? transactionType,
    double? quantityChange,
    double? previousStock,
    double? newStock,
    String? referenceId,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
  }) {
    return InventoryTransactionModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      transactionType: transactionType ?? this.transactionType,
      quantityChange: quantityChange ?? this.quantityChange,
      previousStock: previousStock ?? this.previousStock,
      newStock: newStock ?? this.newStock,
      referenceId: referenceId ?? this.referenceId,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'InventoryTransactionModel(id: $id, productId: $productId, type: $transactionType, change: $quantityChange)';
  }
}
