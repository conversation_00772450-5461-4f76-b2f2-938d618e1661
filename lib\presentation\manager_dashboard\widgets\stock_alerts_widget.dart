import 'package:flutter/material.dart';
import '../../../core/models/stock_alert_model.dart';
import '../../../core/services/inventory_service.dart';

class StockAlertsWidget extends StatelessWidget {
  final List<StockAlertModel> alerts;
  final VoidCallback? onAlertResolved;

  const StockAlertsWidget({
    Key? key,
    required this.alerts,
    this.onAlertResolved,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.orange.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  'Stock Alerts',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${alerts.length}',
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: alerts.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final alert = alerts[index];
                return _buildAlertItem(context, alert);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(BuildContext context, StockAlertModel alert) {
    Color alertColor;
    IconData alertIcon;

    switch (alert.alertType) {
      case AlertType.outOfStock:
        alertColor = Colors.red;
        alertIcon = Icons.remove_circle;
        break;
      case AlertType.lowStock:
        alertColor = Colors.orange;
        alertIcon = Icons.warning;
        break;
      case AlertType.overstock:
        alertColor = Colors.blue;
        alertIcon = Icons.trending_up;
        break;
    }

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: alertColor.withValues(alpha: 0.1),
        child: Icon(
          alertIcon,
          color: alertColor,
          size: 20,
        ),
      ),
      title: Text(
        alert.alertMessage,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
      subtitle: Text(
        'Created: ${_formatDate(alert.createdAt)}',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${alert.currentStock.toStringAsFixed(1)} units',
            style: TextStyle(
              color: alertColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.check_circle_outline),
            color: Colors.green,
            iconSize: 20,
            onPressed: () => _resolveAlert(context, alert),
            tooltip: 'Mark as resolved',
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Future<void> _resolveAlert(BuildContext context, StockAlertModel alert) async {
    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Resolve Alert'),
          content: const Text('Are you sure you want to mark this alert as resolved?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Resolve'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Show loading
        if (context.mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        await InventoryService.instance.resolveStockAlert(alert.id);

        if (context.mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Alert resolved successfully'),
              backgroundColor: Colors.green,
            ),
          );

          onAlertResolved?.call();
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog if open
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resolve alert: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
