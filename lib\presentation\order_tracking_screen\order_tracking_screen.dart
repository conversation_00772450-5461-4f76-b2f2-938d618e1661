import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../routes/app_routes.dart';
import './widgets/delivery_map_widget.dart';
import './widgets/order_details_widget.dart';
import './widgets/order_status_widget.dart';
import './widgets/delivery_person_widget.dart';

class OrderTrackingScreen extends StatefulWidget {
  const OrderTrackingScreen({Key? key}) : super(key: key);

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  String _orderNumber = '';
  OrderStatus _currentStatus = OrderStatus.confirmed;

  OrderInfo? _orderInfo;

  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    _animationController.forward();
    _loadOrderInfo();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments = ModalRoute.of(context)?.settings.arguments as String?;
    _orderNumber = arguments ?? 'CM${DateTime.now().millisecondsSinceEpoch}';
  }

  void _loadOrderInfo() {
    // Mock order data - in real app, this would come from API
    _orderInfo = OrderInfo(
      orderNumber: _orderNumber,
      orderDate: DateTime.now().subtract(const Duration(hours: 2)),
      deliveryAddress: '123 Westlands Road, Westlands, Nairobi',
      estimatedDelivery: DateTime.now().add(const Duration(minutes: 25)),
      deliveryPerson: DeliveryPersonInfo(
        name: 'Samuel Kiprotich',
        phone: '+254 712 345 678',
        rating: 4.8,
        profileImage:
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        vehicleType: 'Motorcycle',
        vehicleNumber: 'KCA 123B',
      ),
      items: [
        OrderItem(
          name: 'Fresh Milk 1L',
          quantity: 2,
          price: 120.0,
          imageUrl:
              'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=100',
        ),
        OrderItem(
          name: 'White Bread Loaf',
          quantity: 1,
          price: 85.0,
          imageUrl:
              'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=100',
        ),
      ],
      total: 325.0,
    );
    setState(() {});
  }

  Future<void> _onRefresh() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    _loadOrderInfo();
  }

  void _callDeliveryPerson() {
    if (_orderInfo?.deliveryPerson?.phone != null) {
      // In real app, would launch phone dialer
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Calling ${_orderInfo!.deliveryPerson!.name}...'),
        ),
      );
    }
  }

  void _messageDeliveryPerson() {
    if (_orderInfo?.deliveryPerson != null) {
      // In real app, would open messaging/chat
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Opening chat with ${_orderInfo!.deliveryPerson!.name}...',
          ),
        ),
      );
    }
  }

  void _reorderItems() {
    Navigator.pushNamedAndRemoveUntil(
      context,
      AppRoutes.home,
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Track Order'),
        actions: [
          IconButton(
            onPressed: () => _refreshIndicatorKey.currentState?.show(),
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // Order header
              Container(
                margin: EdgeInsets.all(4.w),
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Order #${_orderNumber.substring(_orderNumber.length - 8)}',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 1.h),
                            Text(
                              _orderInfo != null
                                  ? 'Placed ${_formatDate(_orderInfo!.orderDate)}'
                                  : 'Loading...',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color:
                                    Theme.of(
                                      context,
                                    ).textTheme.bodySmall?.color,
                              ),
                            ),
                          ],
                        ),
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 3.w,
                              vertical: 1.h,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(
                                _currentStatus,
                              ).withAlpha(51),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: _getStatusColor(_currentStatus),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getStatusIcon(_currentStatus),
                                  color: _getStatusColor(_currentStatus),
                                  size: 16.sp,
                                ),
                                SizedBox(width: 2.w),
                                Text(
                                  _getStatusText(_currentStatus),
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodySmall?.copyWith(
                                    color: _getStatusColor(_currentStatus),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 3.h),
                    // ETA
                    if (_orderInfo != null) ...[
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(3.w),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withAlpha(26),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: Theme.of(context).primaryColor,
                              size: 20.sp,
                            ),
                            SizedBox(width: 3.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Estimated Delivery',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 0.5.h),
                                  Text(
                                    _formatDeliveryTime(
                                      _orderInfo!.estimatedDelivery,
                                    ),
                                    style: Theme.of(
                                      context,
                                    ).textTheme.titleMedium?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Delivery Map
              if (_orderInfo != null)
                DeliveryMapWidget(
                  deliveryAddress: _orderInfo!.deliveryAddress,
                  deliveryPerson: _orderInfo!.deliveryPerson,
                ),

              // Order Status Timeline
              OrderStatusWidget(
                currentStatus: _currentStatus,
                orderDate: _orderInfo?.orderDate ?? DateTime.now(),
              ),

              // Delivery Person Info
              if (_orderInfo?.deliveryPerson != null)
                DeliveryPersonWidget(
                  deliveryPerson: _orderInfo!.deliveryPerson!,
                  onCall: _callDeliveryPerson,
                  onMessage: _messageDeliveryPerson,
                ),

              // Order Details
              if (_orderInfo != null)
                OrderDetailsWidget(
                  orderInfo: _orderInfo!,
                  onReorder: _reorderItems,
                ),

              SizedBox(height: 2.h),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  String _formatDeliveryTime(DateTime deliveryTime) {
    final now = DateTime.now();
    final difference = deliveryTime.difference(now);

    if (difference.inMinutes <= 0) {
      return 'Arriving now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes';
    } else {
      return '${difference.inHours}h ${difference.inMinutes % 60}m';
    }
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.preparing:
        return Colors.orange;
      case OrderStatus.outForDelivery:
        return Theme.of(context).primaryColor;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return Icons.check_circle_outline;
      case OrderStatus.preparing:
        return Icons.restaurant_menu;
      case OrderStatus.outForDelivery:
        return Icons.delivery_dining;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

// Enums and Data Models
enum OrderStatus { confirmed, preparing, outForDelivery, delivered, cancelled }

class OrderInfo {
  final String orderNumber;
  final DateTime orderDate;
  final String deliveryAddress;
  final DateTime estimatedDelivery;
  final DeliveryPersonInfo? deliveryPerson;
  final List<OrderItem> items;
  final double total;

  const OrderInfo({
    required this.orderNumber,
    required this.orderDate,
    required this.deliveryAddress,
    required this.estimatedDelivery,
    this.deliveryPerson,
    required this.items,
    required this.total,
  });
}

class DeliveryPersonInfo {
  final String name;
  final String phone;
  final double rating;
  final String profileImage;
  final String vehicleType;
  final String vehicleNumber;

  const DeliveryPersonInfo({
    required this.name,
    required this.phone,
    required this.rating,
    required this.profileImage,
    required this.vehicleType,
    required this.vehicleNumber,
  });
}

class OrderItem {
  final String name;
  final int quantity;
  final double price;
  final String imageUrl;

  const OrderItem({
    required this.name,
    required this.quantity,
    required this.price,
    required this.imageUrl,
  });
}
