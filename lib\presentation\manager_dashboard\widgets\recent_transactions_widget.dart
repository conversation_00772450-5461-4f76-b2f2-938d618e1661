import 'package:flutter/material.dart';
import '../../../core/models/inventory_transaction_model.dart';
import '../../../theme/app_theme.dart';

class RecentTransactionsWidget extends StatelessWidget {
  final List<InventoryTransactionModel> transactions;

  const RecentTransactionsWidget({
    Key? key,
    required this.transactions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: AppTheme.primaryLight,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to full transactions view
                    // This would be implemented later
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: transactions.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                return _buildTransactionItem(context, transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(BuildContext context, InventoryTransactionModel transaction) {
    Color transactionColor;
    IconData transactionIcon;
    String transactionLabel;

    switch (transaction.transactionType) {
      case TransactionType.stockIn:
        transactionColor = Colors.green;
        transactionIcon = Icons.add_circle;
        transactionLabel = 'Stock In';
        break;
      case TransactionType.stockOut:
        transactionColor = Colors.red;
        transactionIcon = Icons.remove_circle;
        transactionLabel = 'Stock Out';
        break;
      case TransactionType.adjustment:
        transactionColor = Colors.blue;
        transactionIcon = Icons.tune;
        transactionLabel = 'Adjustment';
        break;
      case TransactionType.sale:
        transactionColor = Colors.purple;
        transactionIcon = Icons.shopping_cart;
        transactionLabel = 'Sale';
        break;
      case TransactionType.returnItem:
        transactionColor = Colors.orange;
        transactionIcon = Icons.undo;
        transactionLabel = 'Return';
        break;
    }

    final isPositive = transaction.quantityChange > 0;
    final changeText = '${isPositive ? '+' : ''}${transaction.quantityChange.toStringAsFixed(1)}';

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: transactionColor.withValues(alpha: 0.1),
        child: Icon(
          transactionIcon,
          color: transactionColor,
          size: 20,
        ),
      ),
      title: Text(
        transactionLabel,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (transaction.notes != null && transaction.notes!.isNotEmpty)
            Text(
              transaction.notes!,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          Text(
            _formatDate(transaction.createdAt),
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 11,
            ),
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            changeText,
            style: TextStyle(
              color: transactionColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          Text(
            '→ ${transaction.newStock.toStringAsFixed(1)}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
