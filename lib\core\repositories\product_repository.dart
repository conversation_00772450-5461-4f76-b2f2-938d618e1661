import 'dart:convert';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../services/database_service.dart';
import '../services/api_service.dart';

class ProductRepository {
  final DatabaseService _db = DatabaseService.instance;
  final ApiService _api = ApiService.instance;

  /// Get all categories using PostgREST API
  Future<List<CategoryModel>> getCategories() async {
    try {
      final response = await _api.get('/categories', queryParameters: {
        'is_active': 'eq.true',
        'order': 'sort_order.asc,name.asc',
        'select': 'id,name,description,image_url,icon_name,sort_order,is_active,created_at,updated_at'
      });

      if (response['data'] != null) {
        final List<dynamic> categoriesData = response['data'];
        return categoriesData.map((data) => CategoryModel.fromMap(data)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  /// Get products by category using PostgREST API
  Future<List<ProductModel>> getProductsByCategory(String categoryId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _api.get('/products', queryParameters: {
        'category_id': 'eq.$categoryId',
        'is_available': 'eq.true',
        'order': 'name.asc',
        'limit': limit.toString(),
        'offset': offset.toString(),
        'select': 'id,name,description,price,discount_price,category_id,image_urls,unit,stock,is_available,rating,review_count,nutrition_info,created_at,updated_at,categories(name)'
      });

      if (response['data'] != null) {
        final List<dynamic> productsData = response['data'];
        return productsData.map((data) {
          // Handle category name from join
          if (data['categories'] != null && data['categories'] is Map) {
            data['category_name'] = data['categories']['name'];
          }
          return ProductModel.fromMap(data);
        }).toList();
      }

      return [];
    } catch (e) {
      // Fallback to database if API fails
      try {
        final results = await _db.query(
          '''
          SELECT p.id, p.name, p.description, p.price, p.discount_price,
                 p.category_id, c.name as category_name, p.image_urls,
                 p.unit, p.stock, p.is_available, p.rating, p.review_count,
                 p.nutrition_info, p.created_at, p.updated_at
          FROM products p
          LEFT JOIN categories c ON p.category_id = c.id
          WHERE p.category_id = @categoryId AND p.is_available = true
          ORDER BY p.name ASC
          LIMIT @limit OFFSET @offset
          ''',
          parameters: {
            'categoryId': categoryId,
            'limit': limit,
            'offset': offset,
          },
        );
        return results.map((result) => ProductModel.fromMap(result)).toList();
      } catch (dbError) {
        throw Exception('Failed to get products by category: $e');
      }
    }
  }

  /// Search products
  Future<List<ProductModel>> searchProducts(String query, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _api.get('/products', queryParameters: {
        'or': '(name.ilike.*$query*,description.ilike.*$query*)',
        'is_available': 'eq.true',
        'order': 'name.asc',
        'limit': limit.toString(),
        'offset': offset.toString(),
        'select': 'id,name,description,price,discount_price,category_id,image_urls,unit,stock,is_available,rating,review_count,nutrition_info,created_at,updated_at,categories(name)'
      });

      if (response['data'] != null) {
        final List<dynamic> productsData = response['data'];
        return productsData.map((data) => ProductModel.fromMap(data)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }

  /// Get popular products
  Future<List<ProductModel>> getPopularProducts({int limit = 10}) async {
    try {
      final results = await _db.query(
        '''
        SELECT p.id, p.name, p.description, p.price, p.discount_price, 
               p.category_id, c.name as category_name, p.image_urls, 
               p.unit, p.stock, p.is_available, p.rating, p.review_count,
               p.nutrition_info, p.created_at, p.updated_at
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_available = true AND p.rating IS NOT NULL
        ORDER BY p.rating DESC, p.review_count DESC
        LIMIT @limit
        ''',
        parameters: {'limit': limit},
      );

      return results.map((result) => ProductModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get popular products: $e');
    }
  }

  /// Get featured/discounted products
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10}) async {
    try {
      final results = await _db.query(
        '''
        SELECT p.id, p.name, p.description, p.price, p.discount_price, 
               p.category_id, c.name as category_name, p.image_urls, 
               p.unit, p.stock, p.is_available, p.rating, p.review_count,
               p.nutrition_info, p.created_at, p.updated_at
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_available = true AND p.discount_price IS NOT NULL
        ORDER BY ((p.price - p.discount_price) / p.price) DESC
        LIMIT @limit
        ''',
        parameters: {'limit': limit},
      );

      return results.map((result) => ProductModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get featured products: $e');
    }
  }

  /// Get product by ID
  Future<ProductModel?> getProductById(String productId) async {
    try {
      final response = await _api.get('/products', queryParameters: {
        'id': 'eq.$productId',
        'select': 'id,name,description,price,discount_price,category_id,image_urls,unit,stock,is_available,rating,review_count,nutrition_info,created_at,updated_at,categories(name)'
      });

      if (response['data'] != null && (response['data'] as List).isNotEmpty) {
        final productData = (response['data'] as List).first;
        return ProductModel.fromMap(productData);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product: $e');
    }
  }

  /// Get all products with pagination
  Future<List<ProductModel>> getAllProducts({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final results = await _db.query(
        '''
        SELECT p.id, p.name, p.description, p.price, p.discount_price, 
               p.category_id, c.name as category_name, p.image_urls, 
               p.unit, p.stock, p.is_available, p.rating, p.review_count,
               p.nutrition_info, p.created_at, p.updated_at
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_available = true
        ORDER BY p.created_at DESC
        LIMIT @limit OFFSET @offset
        ''',
        parameters: {
          'limit': limit,
          'offset': offset,
        },
      );

      return results.map((result) => ProductModel.fromMap(result)).toList();
    } catch (e) {
      throw Exception('Failed to get products: $e');
    }
  }

  /// Update product stock
  Future<bool> updateProductStock(String productId, double newStock) async {
    try {
      final affectedRows = await _db.execute(
        '''
        UPDATE products 
        SET stock = @newStock, updated_at = NOW()
        WHERE id = @productId
        ''',
        parameters: {
          'productId': productId,
          'newStock': newStock,
        },
      );

      return affectedRows > 0;
    } catch (e) {
      throw Exception('Failed to update product stock: $e');
    }
  }

  /// Check product availability
  Future<bool> isProductAvailable(String productId, double quantity) async {
    try {
      final result = await _db.queryOne(
        '''
        SELECT stock, is_available
        FROM products
        WHERE id = @productId
        ''',
        parameters: {'productId': productId},
      );

      if (result == null) return false;

      final stock = double.tryParse(result['stock']?.toString() ?? '0') ?? 0.0;
      final isAvailable = result['is_available'] == true || result['is_available'] == 1;

      return isAvailable && stock >= quantity;
    } catch (e) {
      throw Exception('Failed to check product availability: $e');
    }
  }

  /// Get product by SKU
  Future<ProductModel?> getProductBySku(String sku) async {
    try {
      final result = await _db.queryOne(
        '''
        SELECT p.id, p.sku, p.name, p.description, p.price, p.discount_price,
               p.category_id, c.name as category_name, p.image_urls,
               p.unit, p.stock, p.min_stock_level, p.max_stock_level,
               p.is_available, p.rating, p.review_count,
               p.nutrition_info, p.created_at, p.updated_at
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.sku = @sku
        ''',
        parameters: {'sku': sku},
      );

      return result != null ? ProductModel.fromMap(result) : null;
    } catch (e) {
      throw Exception('Failed to get product by SKU: $e');
    }
  }

  /// Update product details (for managers)
  Future<bool> updateProduct(ProductModel product) async {
    try {
      final affectedRows = await _db.execute(
        '''
        UPDATE products
        SET name = @name, description = @description, price = @price,
            discount_price = @discountPrice, category_id = @categoryId,
            unit = @unit, min_stock_level = @minStockLevel,
            max_stock_level = @maxStockLevel, is_available = @isAvailable,
            nutrition_info = @nutritionInfo, updated_at = NOW()
        WHERE id = @productId
        ''',
        parameters: {
          'productId': product.id,
          'name': product.name,
          'description': product.description,
          'price': product.price,
          'discountPrice': product.discountPrice,
          'categoryId': product.categoryId,
          'unit': product.unit,
          'minStockLevel': product.minStockLevel,
          'maxStockLevel': product.maxStockLevel,
          'isAvailable': product.isAvailable,
          'nutritionInfo': product.nutritionInfo != null ?
              json.encode(product.nutritionInfo) : null,
        },
      );

      return affectedRows > 0;
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }
}
