
import 'package:postgres/postgres.dart';
import 'package:flutter/foundation.dart';
import '../utils/env_config.dart';

class DatabaseService {
  static DatabaseService? _instance;
  Connection? _connection;
  
  DatabaseService._internal();
  
  static DatabaseService get instance {
    _instance ??= DatabaseService._internal();
    return _instance!;
  }
  
  /// Initialize database connection
  Future<void> initialize() async {
    try {
      if (_connection != null) {
        await _connection!.close();
      }
      
      final dbUrl = EnvConfig.databaseUrl;
      final uri = Uri.parse(dbUrl);
      
      _connection = await Connection.open(
        Endpoint(
          host: uri.host,
          port: uri.port,
          database: uri.pathSegments.first,
          username: uri.userInfo.split(':')[0],
          password: uri.userInfo.split(':')[1],
        ),
        settings: ConnectionSettings(
          sslMode: SslMode.require,
          connectTimeout: const Duration(seconds: 30),
          queryTimeout: const Duration(seconds: 30),
        ),
      );
      
      if (kDebugMode) {
        print('Database connected successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Database connection error: $e');
      }
      rethrow;
    }
  }
  
  /// Execute a query and return results
  Future<List<Map<String, dynamic>>> query(
    String sql, {
    Map<String, dynamic>? parameters,
  }) async {
    try {
      if (_connection == null) {
        await initialize();
      }
      
      final result = await _connection!.execute(
        Sql.named(sql),
        parameters: parameters ?? {},
      );
      
      return result.map((row) => row.toColumnMap()).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Query error: $e');
      }
      rethrow;
    }
  }
  
  /// Execute a query and return a single result
  Future<Map<String, dynamic>?> queryOne(
    String sql, {
    Map<String, dynamic>? parameters,
  }) async {
    final results = await query(sql, parameters: parameters);
    return results.isNotEmpty ? results.first : null;
  }
  
  /// Execute an insert/update/delete query
  Future<int> execute(
    String sql, {
    Map<String, dynamic>? parameters,
  }) async {
    try {
      if (_connection == null) {
        await initialize();
      }
      
      final result = await _connection!.execute(
        Sql.named(sql),
        parameters: parameters ?? {},
      );
      
      return result.affectedRows;
    } catch (e) {
      if (kDebugMode) {
        print('Execute error: $e');
      }
      rethrow;
    }
  }
  
  /// Execute multiple queries in a transaction
  Future<T> transaction<T>(Future<T> Function() action) async {
    if (_connection == null) {
      await initialize();
    }
    
    return await _connection!.runTx((ctx) async {
      return await action();
    });
  }
  
  /// Close database connection
  Future<void> close() async {
    if (_connection != null) {
      await _connection!.close();
      _connection = null;
    }
  }
  
  /// Check if connection is active
  bool get isConnected => _connection != null;
}
