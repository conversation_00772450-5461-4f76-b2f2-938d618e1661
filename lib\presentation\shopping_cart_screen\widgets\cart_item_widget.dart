import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_image_widget.dart';
import '../shopping_cart_screen.dart';

class CartItemWidget extends StatelessWidget {
  final CartItem item;
  final bool isEditMode;
  final Function(String, int) onQuantityChanged;
  final Function(String) onRemove;

  const CartItemWidget({
    Key? key,
    required this.item,
    required this.isEditMode,
    required this.onQuantityChanged,
    required this.onRemove,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(item.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 5.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.error,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.delete_outline,
          color: Colors.white,
          size: 24.sp,
        ),
      ),
      onDismissed: (direction) {
        onRemove(item.id);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 2.h),
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[100],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CustomImageWidget(
                  imageUrl: item.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
            ),

            SizedBox(width: 3.w),

            // Product details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name and out of stock indicator
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item.name,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (!item.isInStock)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 2.w,
                            vertical: 0.5.h,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .error
                                .withAlpha(26),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Out of Stock',
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.error,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ),
                    ],
                  ),

                  SizedBox(height: 0.5.h),

                  // Price per unit
                  Text(
                    'KES ${item.price.toStringAsFixed(0)} ${item.unit}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                  ),

                  SizedBox(height: 1.h),

                  // Quantity selector and subtotal
                  Row(
                    children: [
                      // Quantity selector
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Theme.of(context).dividerColor,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: item.quantity > 1
                                  ? () => onQuantityChanged(
                                      item.id, item.quantity - 1)
                                  : null,
                              icon: Icon(
                                Icons.remove,
                                size: 18.sp,
                                color: item.quantity > 1
                                    ? Theme.of(context).primaryColor
                                    : Theme.of(context).disabledColor,
                              ),
                              padding: EdgeInsets.all(1.w),
                              constraints: BoxConstraints(
                                minWidth: 8.w,
                                minHeight: 8.w,
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 3.w),
                              child: Text(
                                '${item.quantity}',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                            IconButton(
                              onPressed: () =>
                                  onQuantityChanged(item.id, item.quantity + 1),
                              icon: Icon(
                                Icons.add,
                                size: 18.sp,
                                color: Theme.of(context).primaryColor,
                              ),
                              padding: EdgeInsets.all(1.w),
                              constraints: BoxConstraints(
                                minWidth: 8.w,
                                minHeight: 8.w,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const Spacer(),

                      // Subtotal
                      Text(
                        'KES ${(item.price * item.quantity).toStringAsFixed(0)}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ],
                  ),

                  // Replacement suggestions for out of stock items
                  if (!item.isInStock) ...[
                    SizedBox(height: 1.h),
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withAlpha(13),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).primaryColor.withAlpha(51),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.lightbulb_outline,
                            color: Theme.of(context).primaryColor,
                            size: 16.sp,
                          ),
                          SizedBox(width: 2.w),
                          Expanded(
                            child: Text(
                              'Similar item available - Tap to replace',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Edit mode actions
            if (isEditMode)
              Column(
                children: [
                  IconButton(
                    onPressed: () => onRemove(item.id),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.error,
                      size: 20.sp,
                    ),
                    padding: EdgeInsets.all(1.w),
                  ),
                  TextButton(
                    onPressed: () {
                      // Save for later functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Moved to wishlist')),
                      );
                    },
                    child: Text(
                      'Save for\nlater',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}