import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../repositories/user_repository.dart';
import '../repositories/product_repository.dart';
import '../repositories/cart_repository.dart';

class ApiTestService {
  static ApiTestService? _instance;
  final ApiService _api = ApiService.instance;
  final UserRepository _userRepo = UserRepository();
  final ProductRepository _productRepo = ProductRepository();
  final CartRepository _cartRepo = CartRepository();
  
  ApiTestService._internal();
  
  static ApiTestService get instance {
    _instance ??= ApiTestService._internal();
    return _instance!;
  }
  
  /// Test all API endpoints
  Future<Map<String, dynamic>> testAllEndpoints() async {
    final results = <String, dynamic>{};
    
    if (kDebugMode) {
      print('🧪 Starting API Tests...');
    }
    
    // Test 1: Check API connectivity
    results['connectivity'] = await _testConnectivity();
    
    // Test 2: Check database schema
    results['schema'] = await _testDatabaseSchema();
    
    // Test 3: Test user registration
    results['registration'] = await _testUserRegistration();
    
    // Test 4: Test user login
    results['login'] = await _testUserLogin();
    
    // Test 5: Test categories endpoint
    results['categories'] = await _testCategories();
    
    // Test 6: Test products endpoint
    results['products'] = await _testProducts();
    
    // Test 7: Test cart operations
    results['cart'] = await _testCartOperations();
    
    if (kDebugMode) {
      print('🧪 API Tests Complete');
      print('Results: $results');
    }
    
    return results;
  }
  
  /// Test API connectivity
  Future<Map<String, dynamic>> _testConnectivity() async {
    try {
      final response = await _api.get('/');
      return {
        'success': true,
        'message': 'API is reachable',
        'swagger_info': response['info'] ?? 'No info available'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
  
  /// Test database schema existence
  Future<Map<String, dynamic>> _testDatabaseSchema() async {
    final tables = ['users', 'categories', 'products', 'cart_items', 'orders'];
    final results = <String, bool>{};
    
    for (final table in tables) {
      try {
        await _api.get('/$table', queryParameters: {'limit': '1'});
        results[table] = true;
      } catch (e) {
        results[table] = false;
      }
    }
    
    final allTablesExist = results.values.every((exists) => exists);
    
    return {
      'success': allTablesExist,
      'tables': results,
      'message': allTablesExist 
          ? 'All required tables exist' 
          : 'Some tables are missing. Run database_schema.sql'
    };
  }
  
  /// Test user registration
  Future<Map<String, dynamic>> _testUserRegistration() async {
    try {
      final testEmail = 'test_${DateTime.now().millisecondsSinceEpoch}@example.com';
      
      final user = await _userRepo.registerUser(
        email: testEmail,
        password: 'test123',
        firstName: 'Test',
        lastName: 'User',
        phone: '+1234567890',
      );
      
      return {
        'success': true,
        'message': 'User registration successful',
        'user_id': user.id,
        'email': user.email
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
  
  /// Test user login
  Future<Map<String, dynamic>> _testUserLogin() async {
    try {
      // First create a test user
      final testEmail = 'login_test_${DateTime.now().millisecondsSinceEpoch}@example.com';
      
      await _userRepo.registerUser(
        email: testEmail,
        password: 'test123',
        firstName: 'Login',
        lastName: 'Test',
      );
      
      // Then try to login
      final user = await _userRepo.loginUser(
        email: testEmail,
        password: 'test123',
      );
      
      if (user != null) {
        return {
          'success': true,
          'message': 'User login successful',
          'user_id': user.id,
          'email': user.email
        };
      } else {
        return {
          'success': false,
          'error': 'Login returned null user'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
  
  /// Test categories endpoint
  Future<Map<String, dynamic>> _testCategories() async {
    try {
      final categories = await _productRepo.getCategories();
      
      return {
        'success': true,
        'message': 'Categories retrieved successfully',
        'count': categories.length,
        'categories': categories.take(3).map((c) => {
          'id': c.id,
          'name': c.name
        }).toList()
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
  
  /// Test products endpoint
  Future<Map<String, dynamic>> _testProducts() async {
    try {
      // First get categories to test products by category
      final categories = await _productRepo.getCategories();
      
      if (categories.isEmpty) {
        return {
          'success': false,
          'error': 'No categories found to test products'
        };
      }
      
      final products = await _productRepo.getProductsByCategory(
        categories.first.id,
        limit: 5
      );
      
      return {
        'success': true,
        'message': 'Products retrieved successfully',
        'category': categories.first.name,
        'count': products.length,
        'products': products.take(3).map((p) => {
          'id': p.id,
          'name': p.name,
          'price': p.price
        }).toList()
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
  
  /// Test cart operations
  Future<Map<String, dynamic>> _testCartOperations() async {
    try {
      // Create a test user first
      final testEmail = 'cart_test_${DateTime.now().millisecondsSinceEpoch}@example.com';
      
      final user = await _userRepo.registerUser(
        email: testEmail,
        password: 'test123',
        firstName: 'Cart',
        lastName: 'Test',
      );
      
      // Get cart items (should be empty)
      final cartItems = await _cartRepo.getCartItems(user.id);
      
      return {
        'success': true,
        'message': 'Cart operations test completed',
        'user_id': user.id,
        'cart_items_count': cartItems.length
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
  
  /// Get a summary of API health
  Future<String> getApiHealthSummary() async {
    final results = await testAllEndpoints();
    
    final connectivity = results['connectivity']['success'] ?? false;
    final schema = results['schema']['success'] ?? false;
    final registration = results['registration']['success'] ?? false;
    final login = results['login']['success'] ?? false;
    final categories = results['categories']['success'] ?? false;
    final products = results['products']['success'] ?? false;
    final cart = results['cart']['success'] ?? false;
    
    final successCount = [connectivity, schema, registration, login, categories, products, cart]
        .where((test) => test).length;
    
    if (successCount == 7) {
      return '✅ All API endpoints are working correctly!';
    } else if (successCount >= 5) {
      return '⚠️ Most API endpoints are working. Check logs for details.';
    } else if (successCount >= 3) {
      return '🔶 Some API endpoints are working. Database schema may need setup.';
    } else {
      return '❌ API endpoints are not working. Check configuration and database setup.';
    }
  }
}
