const { Client } = require('pg');
const fs = require('fs');

async function updateDatabase() {
    // Read environment configuration
    const envConfig = JSON.parse(fs.readFileSync('env.json', 'utf8'));
    
    // Create PostgreSQL client
    const client = new Client({
        connectionString: envConfig.DATABASE_URL,
        ssl: {
            rejectUnauthorized: false
        }
    });

    try {
        console.log('Connecting to database...');
        await client.connect();
        console.log('Connected successfully!\n');

        // Create user role enum if it doesn't exist
        console.log('Creating user role enum...');
        try {
            await client.query(`CREATE TYPE user_role AS ENUM ('customer', 'manager', 'admin')`);
            console.log('✅ User role enum created');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ User role enum already exists');
            } else {
                console.log('❌ Error creating user role enum:', error.message);
            }
        }

        // Add role column to users table if it doesn't exist
        console.log('\nAdding role column to users table...');
        try {
            await client.query(`ALTER TABLE users ADD COLUMN role user_role DEFAULT 'customer'`);
            console.log('✅ Role column added to users table');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ Role column already exists in users table');
            } else {
                console.log('❌ Error adding role column:', error.message);
            }
        }

        // Create inventory transactions table
        console.log('\nCreating inventory_transactions table...');
        try {
            await client.query(`
                CREATE TABLE inventory_transactions (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
                    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('stock_in', 'stock_out', 'adjustment', 'sale', 'return')),
                    quantity_change DECIMAL(10,2) NOT NULL,
                    previous_stock DECIMAL(10,2) NOT NULL,
                    new_stock DECIMAL(10,2) NOT NULL,
                    reference_id UUID,
                    notes TEXT,
                    created_by UUID REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT NOW()
                )
            `);
            console.log('✅ inventory_transactions table created');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ inventory_transactions table already exists');
            } else {
                console.log('❌ Error creating inventory_transactions table:', error.message);
            }
        }

        // Create stock alerts table
        console.log('\nCreating stock_alerts table...');
        try {
            await client.query(`
                CREATE TABLE stock_alerts (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
                    alert_type VARCHAR(20) NOT NULL CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock')),
                    threshold_value DECIMAL(10,2),
                    current_stock DECIMAL(10,2),
                    is_resolved BOOLEAN DEFAULT false,
                    resolved_at TIMESTAMP,
                    resolved_by UUID REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT NOW()
                )
            `);
            console.log('✅ stock_alerts table created');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ stock_alerts table already exists');
            } else {
                console.log('❌ Error creating stock_alerts table:', error.message);
            }
        }

        // Create inventory snapshots table
        console.log('\nCreating inventory_snapshots table...');
        try {
            await client.query(`
                CREATE TABLE inventory_snapshots (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
                    snapshot_date DATE NOT NULL,
                    opening_stock DECIMAL(10,2) NOT NULL,
                    closing_stock DECIMAL(10,2) NOT NULL,
                    total_stock_in DECIMAL(10,2) DEFAULT 0,
                    total_stock_out DECIMAL(10,2) DEFAULT 0,
                    total_sales DECIMAL(10,2) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT NOW(),
                    UNIQUE(product_id, snapshot_date)
                )
            `);
            console.log('✅ inventory_snapshots table created');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ inventory_snapshots table already exists');
            } else {
                console.log('❌ Error creating inventory_snapshots table:', error.message);
            }
        }

        // Create default manager user
        console.log('\nCreating default manager user...');
        try {
            await client.query(`
                INSERT INTO users (email, password_hash, first_name, last_name, phone, role) 
                VALUES ('<EMAIL>', 'hashed_password_123', 'Store', 'Manager', '+254700000000', 'manager')
                ON CONFLICT (email) DO NOTHING
            `);
            console.log('✅ Default manager user created');
        } catch (error) {
            console.log('❌ Error creating manager user:', error.message);
        }

        console.log('\n=== DATABASE UPDATE COMPLETE ===');
        console.log('Running final verification...\n');

        // Final verification
        const tablesResult = await client.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);
        
        console.log('All tables:');
        tablesResult.rows.forEach(row => {
            console.log(`✅ ${row.table_name}`);
        });

        // Check user count
        const userCount = await client.query('SELECT COUNT(*) as count FROM users');
        console.log(`\n👥 Total users: ${userCount.rows[0].count}`);

    } catch (error) {
        console.error('Error:', error.message);
        if (error.detail) {
            console.error('Detail:', error.detail);
        }
    } finally {
        await client.end();
        console.log('\nDatabase connection closed.');
    }
}

updateDatabase();
