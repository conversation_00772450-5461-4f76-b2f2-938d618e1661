import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../user_profile_screen.dart';

class ProfileStatsWidget extends StatefulWidget {
  final UserProfile userProfile;

  const ProfileStatsWidget({Key? key, required this.userProfile})
    : super(key: key);

  @override
  State<ProfileStatsWidget> createState() => _ProfileStatsWidgetState();
}

class _ProfileStatsWidgetState extends State<ProfileStatsWidget>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: Duration(milliseconds: 800 + (index * 100)),
        vsync: this,
      ),
    );

    _animations =
        _controllers.map((controller) {
          return Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(parent: controller, curve: Curves.elasticOut),
          );
        }).toList();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final stats = [
      ProfileStat(
        title: 'Total Orders',
        value: widget.userProfile.totalOrders.toString(),
        icon: Icons.shopping_bag_outlined,
        color: Colors.blue,
      ),
      ProfileStat(
        title: 'Total Spent',
        value: 'KES ${widget.userProfile.totalSpent.toStringAsFixed(0)}',
        icon: Icons.account_balance_wallet_outlined,
        color: Colors.green,
      ),
      ProfileStat(
        title: 'Favorites',
        value: widget.userProfile.favoriteItems.toString(),
        icon: Icons.favorite_outline,
        color: Colors.red,
      ),
    ];

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Row(
        children: List.generate(stats.length, (index) {
          return Expanded(
            child: AnimatedBuilder(
              animation: _animations[index],
              builder: (context, child) {
                return Transform.scale(
                  scale: 0.8 + (0.2 * _animations[index].value),
                  child: Opacity(
                    opacity: _animations[index].value,
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: 1.w),
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(13),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(3.w),
                            decoration: BoxDecoration(
                              color: stats[index].color.withAlpha(26),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              stats[index].icon,
                              color: stats[index].color,
                              size: 24.sp,
                            ),
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            stats[index].value,
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: stats[index].color,
                            ),
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            stats[index].title,
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }),
      ),
    );
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }
}

class ProfileStat {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const ProfileStat({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });
}
