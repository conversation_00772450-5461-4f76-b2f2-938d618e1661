import '../models/cart_model.dart';

import '../services/database_service.dart';
import '../services/api_service.dart';
import 'product_repository.dart';

class CartRepository {
  final DatabaseService _db = DatabaseService.instance;
  final ApiService _api = ApiService.instance;
  final ProductRepository _productRepository = ProductRepository();

  /// Get user's cart items using PostgREST API
  Future<List<CartItemModel>> getCartItems(String userId) async {
    try {
      final response = await _api.get('/cart_items', queryParameters: {
        'user_id': 'eq.$userId',
        'order': 'created_at.desc',
        'select': 'id,user_id,product_id,quantity,created_at,updated_at'
      });

      final cartItems = <CartItemModel>[];

      if (response['data'] != null) {
        final List<dynamic> cartData = response['data'];

        for (final cartItem in cartData) {
          final productId = cartItem['product_id']?.toString() ?? '';
          final product = await _productRepository.getProductById(productId);

          if (product != null) {
            cartItems.add(CartItemModel.fromMap(cartItem, product));
          }
        }
      }

      return cartItems;
    } catch (e) {
      // Fallback to database if API fails
      try {
        final results = await _db.query(
          '''
          SELECT ci.id, ci.user_id, ci.product_id, ci.quantity, ci.created_at, ci.updated_at
          FROM cart_items ci
          WHERE ci.user_id = @userId
          ORDER BY ci.created_at DESC
          ''',
          parameters: {'userId': userId},
        );

        final cartItems = <CartItemModel>[];

        for (final result in results) {
          final productId = result['product_id']?.toString() ?? '';
          final product = await _productRepository.getProductById(productId);

          if (product != null) {
            cartItems.add(CartItemModel.fromMap(result, product));
          }
        }

        return cartItems;
      } catch (dbError) {
        throw Exception('Failed to get cart items: $e');
      }
    }
  }

  /// Add item to cart
  Future<CartItemModel> addToCart({
    required String userId,
    required String productId,
    required double quantity,
  }) async {
    try {
      // Check if item already exists in cart
      final existingItem = await _db.queryOne(
        '''
        SELECT id, user_id, product_id, quantity, created_at, updated_at
        FROM cart_items
        WHERE user_id = @userId AND product_id = @productId
        ''',
        parameters: {
          'userId': userId,
          'productId': productId,
        },
      );

      Map<String, dynamic> result;

      if (existingItem != null) {
        // Update existing item quantity
        final newQuantity = (double.tryParse(existingItem['quantity']?.toString() ?? '0') ?? 0.0) + quantity;
        
        result = await _db.queryOne(
          '''
          UPDATE cart_items 
          SET quantity = @quantity, updated_at = NOW()
          WHERE user_id = @userId AND product_id = @productId
          RETURNING id, user_id, product_id, quantity, created_at, updated_at
          ''',
          parameters: {
            'userId': userId,
            'productId': productId,
            'quantity': newQuantity,
          },
        ) ?? {};
      } else {
        // Insert new item
        result = await _db.queryOne(
          '''
          INSERT INTO cart_items (user_id, product_id, quantity, created_at, updated_at)
          VALUES (@userId, @productId, @quantity, NOW(), NOW())
          RETURNING id, user_id, product_id, quantity, created_at, updated_at
          ''',
          parameters: {
            'userId': userId,
            'productId': productId,
            'quantity': quantity,
          },
        ) ?? {};
      }

      // Get the product details
      final product = await _productRepository.getProductById(productId);
      if (product == null) {
        throw Exception('Product not found');
      }

      return CartItemModel.fromMap(result, product);
    } catch (e) {
      throw Exception('Failed to add item to cart: $e');
    }
  }

  /// Update cart item quantity
  Future<CartItemModel?> updateCartItem({
    required String userId,
    required String productId,
    required double quantity,
  }) async {
    try {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        await removeFromCart(userId: userId, productId: productId);
        return null;
      }

      final result = await _db.queryOne(
        '''
        UPDATE cart_items 
        SET quantity = @quantity, updated_at = NOW()
        WHERE user_id = @userId AND product_id = @productId
        RETURNING id, user_id, product_id, quantity, created_at, updated_at
        ''',
        parameters: {
          'userId': userId,
          'productId': productId,
          'quantity': quantity,
        },
      );

      if (result == null) {
        throw Exception('Cart item not found');
      }

      // Get the product details
      final product = await _productRepository.getProductById(productId);
      if (product == null) {
        throw Exception('Product not found');
      }

      return CartItemModel.fromMap(result, product);
    } catch (e) {
      throw Exception('Failed to update cart item: $e');
    }
  }

  /// Remove item from cart
  Future<bool> removeFromCart({
    required String userId,
    required String productId,
  }) async {
    try {
      final affectedRows = await _db.execute(
        '''
        DELETE FROM cart_items
        WHERE user_id = @userId AND product_id = @productId
        ''',
        parameters: {
          'userId': userId,
          'productId': productId,
        },
      );

      return affectedRows > 0;
    } catch (e) {
      throw Exception('Failed to remove item from cart: $e');
    }
  }

  /// Clear entire cart
  Future<bool> clearCart(String userId) async {
    try {
      final affectedRows = await _db.execute(
        '''
        DELETE FROM cart_items
        WHERE user_id = @userId
        ''',
        parameters: {'userId': userId},
      );

      return affectedRows >= 0; // Return true even if cart was already empty
    } catch (e) {
      throw Exception('Failed to clear cart: $e');
    }
  }

  /// Get cart summary
  Future<CartSummary> getCartSummary(String userId) async {
    try {
      final cartItems = await getCartItems(userId);
      return CartSummary.fromItems(cartItems);
    } catch (e) {
      throw Exception('Failed to get cart summary: $e');
    }
  }

  /// Get cart item count
  Future<int> getCartItemCount(String userId) async {
    try {
      final result = await _db.queryOne(
        '''
        SELECT COALESCE(SUM(quantity), 0) as total_items
        FROM cart_items
        WHERE user_id = @userId
        ''',
        parameters: {'userId': userId},
      );

      return int.tryParse(result?['total_items']?.toString() ?? '0') ?? 0;
    } catch (e) {
      throw Exception('Failed to get cart item count: $e');
    }
  }

  /// Check if product is in cart
  Future<bool> isProductInCart({
    required String userId,
    required String productId,
  }) async {
    try {
      final result = await _db.queryOne(
        '''
        SELECT id FROM cart_items
        WHERE user_id = @userId AND product_id = @productId
        ''',
        parameters: {
          'userId': userId,
          'productId': productId,
        },
      );

      return result != null;
    } catch (e) {
      return false;
    }
  }
}
