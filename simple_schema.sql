-- ChuiMart Simple Database Schema
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Categories table
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sku VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    discount_price DECIMAL(10,2),
    category_id UUID REFERENCES categories(id),
    image_urls TEXT[],
    unit VARCHAR(20) DEFAULT 'piece',
    stock DECIMAL(10,2) DEFAULT 0,
    min_stock_level DECIMAL(10,2) DEFAULT 5,
    max_stock_level DECIMAL(10,2) DEFAULT 100,
    is_available BOOLEAN DEFAULT true,
    rating DECIMAL(3,2),
    review_count INTEGER DEFAULT 0,
    nutrition_info JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User roles enum
CREATE TYPE user_role AS ENUM ('customer', 'manager', 'admin');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role user_role DEFAULT 'customer',
    is_active BOOLEAN DEFAULT true,
    profile_image_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Cart items table
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Inventory transactions table for tracking stock changes
CREATE TABLE inventory_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('stock_in', 'stock_out', 'adjustment', 'sale', 'return')),
    quantity_change DECIMAL(10,2) NOT NULL,
    previous_stock DECIMAL(10,2) NOT NULL,
    new_stock DECIMAL(10,2) NOT NULL,
    reference_id UUID, -- Can reference order_id, return_id, etc.
    notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Stock alerts table for low stock notifications
CREATE TABLE stock_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    alert_type VARCHAR(20) NOT NULL CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock')),
    threshold_value DECIMAL(10,2),
    current_stock DECIMAL(10,2),
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP,
    resolved_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Daily inventory snapshots for reporting
CREATE TABLE inventory_snapshots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    snapshot_date DATE NOT NULL,
    opening_stock DECIMAL(10,2) NOT NULL,
    closing_stock DECIMAL(10,2) NOT NULL,
    total_stock_in DECIMAL(10,2) DEFAULT 0,
    total_stock_out DECIMAL(10,2) DEFAULT 0,
    total_sales DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(product_id, snapshot_date)
);

-- Sample data
INSERT INTO categories (name, description, icon_name, sort_order) VALUES
('Fruits & Vegetables', 'Fresh fruits and vegetables', 'local_grocery_store', 1),
('Dairy & Eggs', 'Milk, cheese, eggs and dairy products', 'egg', 2),
('Bakery', 'Fresh bread and baked goods', 'bakery_dining', 3),
('Beverages', 'Drinks, juices, and beverages', 'local_drink', 4);

-- Create a default manager user
INSERT INTO users (email, password_hash, first_name, last_name, phone, role) VALUES
('<EMAIL>', 'hashed_password_123', 'Store', 'Manager', '+************', 'manager');

-- Sample products with Kenyan pricing (KES) and SKUs
INSERT INTO products (sku, name, description, price, category_id, image_urls, unit, stock, min_stock_level, max_stock_level, rating, review_count)
SELECT 'FV001', 'Fresh Bananas', 'Sweet and ripe bananas', 150.00, c.id,
       ARRAY['https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400'],
       'kg', 50.0, 10.0, 200.0, 4.5, 120
FROM categories c WHERE c.name = 'Fruits & Vegetables';

INSERT INTO products (sku, name, description, price, discount_price, category_id, image_urls, unit, stock, min_stock_level, max_stock_level, rating, review_count)
SELECT 'DE001', 'Fresh Milk', 'Fresh whole milk', 180.00, 150.00, c.id,
       ARRAY['https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400'],
       'liter', 30.0, 5.0, 100.0, 4.8, 89
FROM categories c WHERE c.name = 'Dairy & Eggs';

INSERT INTO products (sku, name, description, price, category_id, image_urls, unit, stock, min_stock_level, max_stock_level, rating, review_count)
SELECT 'FV002', 'Sukuma Wiki', 'Fresh collard greens', 50.00, c.id,
       ARRAY['https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400'],
       'bunch', 100.0, 20.0, 300.0, 4.7, 156
FROM categories c WHERE c.name = 'Fruits & Vegetables';

INSERT INTO products (sku, name, description, price, category_id, image_urls, unit, stock, min_stock_level, max_stock_level, rating, review_count)
SELECT 'BK001', 'Mandazi', 'Traditional Kenyan sweet bread', 20.00, c.id,
       ARRAY['https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400'],
       'piece', 50.0, 10.0, 200.0, 4.8, 234
FROM categories c WHERE c.name = 'Bakery';

INSERT INTO products (sku, name, description, price, category_id, image_urls, unit, stock, min_stock_level, max_stock_level, rating, review_count)
SELECT 'BV001', 'Coca Cola', 'Classic Coca Cola', 100.00, c.id,
       ARRAY['https://images.unsplash.com/photo-1561758033-d89a9ad46330?w=400'],
       '500ml', 60.0, 12.0, 150.0, 4.2, 89
FROM categories c WHERE c.name = 'Beverages';
