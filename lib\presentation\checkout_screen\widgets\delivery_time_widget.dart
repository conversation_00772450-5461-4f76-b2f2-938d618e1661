import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../checkout_screen.dart';

class DeliveryTimeWidget extends StatefulWidget {
  final DeliveryTimeSlot? selectedTimeSlot;
  final Function(DeliveryTimeSlot) onTimeSlotSelected;

  const DeliveryTimeWidget({
    Key? key,
    required this.selectedTimeSlot,
    required this.onTimeSlotSelected,
  }) : super(key: key);

  @override
  State<DeliveryTimeWidget> createState() => _DeliveryTimeWidgetState();
}

class _DeliveryTimeWidgetState extends State<DeliveryTimeWidget> {
  List<DeliveryTimeSlot> _timeSlots = [];
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadTimeSlots();
  }

  void _loadTimeSlots() {
    // Mock time slots - in real app, this would come from delivery service API
    _timeSlots = [
      DeliveryTimeSlot(
        id: '1',
        displayTime: '9:00 AM - 11:00 AM',
        date: _selectedDate.toString(),
        fee: 0.0, // Free delivery for recommended slot
        isRecommended: true,
        isAvailable: true,
      ),
      DeliveryTimeSlot(
        id: '2',
        displayTime: '11:00 AM - 1:00 PM',
        date: _selectedDate.toString(),
        fee: 50.0,
        isRecommended: false,
        isAvailable: true,
      ),
      DeliveryTimeSlot(
        id: '3',
        displayTime: '1:00 PM - 3:00 PM',
        date: _selectedDate.toString(),
        fee: 75.0,
        isRecommended: false,
        isAvailable: false, // Fully booked
      ),
      DeliveryTimeSlot(
        id: '4',
        displayTime: '3:00 PM - 5:00 PM',
        date: _selectedDate.toString(),
        fee: 100.0,
        isRecommended: false,
        isAvailable: true,
      ),
      DeliveryTimeSlot(
        id: '5',
        displayTime: '5:00 PM - 7:00 PM',
        date: _selectedDate.toString(),
        fee: 150.0, // Peak hour pricing
        isRecommended: false,
        isAvailable: true,
      ),
      DeliveryTimeSlot(
        id: '6',
        displayTime: '7:00 PM - 9:00 PM',
        date: _selectedDate.toString(),
        fee: 200.0, // Premium evening slot
        isRecommended: false,
        isAvailable: true,
      ),
    ];

    // Auto-select recommended slot if none selected
    if (widget.selectedTimeSlot == null) {
      final recommendedSlot = _timeSlots.firstWhere(
        (slot) => slot.isRecommended && slot.isAvailable,
        orElse: () => _timeSlots.first,
      );
      widget.onTimeSlotSelected(recommendedSlot);
    }
  }

  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    _loadTimeSlots(); // Reload slots for new date
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Calendar date picker
          Text(
            'Select Delivery Date',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),

          SizedBox(height: 2.h),

          Container(
            height: 12.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 7, // Show next 7 days
              itemBuilder: (context, index) {
                final date = DateTime.now().add(Duration(days: index));
                final isSelected = date.day == _selectedDate.day &&
                    date.month == _selectedDate.month &&
                    date.year == _selectedDate.year;
                final isToday = date.day == DateTime.now().day &&
                    date.month == DateTime.now().month &&
                    date.year == DateTime.now().year;

                return GestureDetector(
                  onTap: () => _selectDate(date),
                  child: Container(
                    width: 18.w,
                    margin: EdgeInsets.only(right: 3.w),
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).dividerColor,
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          [
                            'Sun',
                            'Mon',
                            'Tue',
                            'Wed',
                            'Thu',
                            'Fri',
                            'Sat'
                          ][date.weekday % 7],
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: isSelected
                                        ? Colors.white
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .color,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          '${date.day}',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: isSelected
                                        ? Colors.white
                                        : Theme.of(context)
                                            .textTheme
                                            .titleMedium!
                                            .color,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        if (isToday) ...[
                          SizedBox(height: 0.5.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 1.w,
                              vertical: 0.2.h,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Colors.white.withAlpha(51)
                                  : Theme.of(context)
                                      .primaryColor
                                      .withAlpha(26),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Today',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: isSelected
                                        ? Colors.white
                                        : Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 8.sp,
                                  ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          SizedBox(height: 3.h),

          // Time slots section
          Row(
            children: [
              Text(
                'Available Time Slots',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 2.w,
                  vertical: 0.5.h,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(26),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.local_shipping_outlined,
                      color: Colors.green,
                      size: 14.sp,
                    ),
                    SizedBox(width: 1.w),
                    Text(
                      'Free delivery available',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.green,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Time slots grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 1,
              childAspectRatio: 4,
              mainAxisSpacing: 12,
            ),
            itemCount: _timeSlots.length,
            itemBuilder: (context, index) {
              final timeSlot = _timeSlots[index];
              final isSelected = widget.selectedTimeSlot?.id == timeSlot.id;

              return GestureDetector(
                onTap: timeSlot.isAvailable
                    ? () => widget.onTimeSlotSelected(timeSlot)
                    : null,
                child: Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: !timeSlot.isAvailable
                        ? Colors.grey.withAlpha(26)
                        : isSelected
                            ? Theme.of(context).primaryColor.withAlpha(26)
                            : Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: !timeSlot.isAvailable
                          ? Colors.grey.withAlpha(77)
                          : isSelected
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).dividerColor,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Time slot icon
                      Container(
                        padding: EdgeInsets.all(2.w),
                        decoration: BoxDecoration(
                          color: !timeSlot.isAvailable
                              ? Colors.grey.withAlpha(26)
                              : isSelected
                                  ? Theme.of(context).primaryColor.withAlpha(51)
                                  : Theme.of(context)
                                      .primaryColor
                                      .withAlpha(26),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          !timeSlot.isAvailable
                              ? Icons.schedule
                              : timeSlot.isRecommended
                                  ? Icons.recommend
                                  : Icons.access_time,
                          color: !timeSlot.isAvailable
                              ? Colors.grey
                              : isSelected
                                  ? Theme.of(context).primaryColor
                                  : Theme.of(context).primaryColor,
                          size: 16.sp,
                        ),
                      ),

                      SizedBox(width: 3.w),

                      // Time slot details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Text(
                                  timeSlot.displayTime,
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: !timeSlot.isAvailable
                                            ? Colors.grey
                                            : isSelected
                                                ? Theme.of(context).primaryColor
                                                : null,
                                      ),
                                ),
                                if (timeSlot.isRecommended) ...[
                                  SizedBox(width: 2.w),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 2.w,
                                      vertical: 0.3.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withAlpha(26),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      'Recommended',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: Colors.green,
                                            fontWeight: FontWeight.w500,
                                          ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            if (!timeSlot.isAvailable)
                              Text(
                                'Fully booked',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Colors.grey,
                                    ),
                              )
                            else if (timeSlot.fee > 0)
                              Text(
                                'Additional fee: KES ${timeSlot.fee.toStringAsFixed(0)}',
                                style: Theme.of(context).textTheme.bodySmall,
                              )
                            else
                              Text(
                                'Free delivery',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Colors.green,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                          ],
                        ),
                      ),

                      // Selection indicator
                      if (timeSlot.isAvailable)
                        Icon(
                          isSelected
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).dividerColor,
                          size: 20.sp,
                        ),
                    ],
                  ),
                ),
              );
            },
          ),

          SizedBox(height: 3.h),

          // Delivery information
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withAlpha(13),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withAlpha(51),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).primaryColor,
                      size: 16.sp,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      'Delivery Information',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Text(
                  '• We will call you 15 minutes before delivery\n'
                  '• Please ensure someone is available to receive the order\n'
                  '• Same-day delivery available for orders placed before 8 PM\n'
                  '• Free delivery for orders above KES 2,000',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
