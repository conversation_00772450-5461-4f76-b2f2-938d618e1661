import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../routes/app_routes.dart';
import './widgets/checkout_order_summary_widget.dart';
import './widgets/delivery_address_widget.dart';
import './widgets/delivery_time_widget.dart';
import './widgets/order_success_widget.dart';
import './widgets/payment_method_widget.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({Key? key}) : super(key: key);

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  bool _isProcessingOrder = false;
  bool _orderCompleted = false;
  String _orderNumber = '';

  // Form data
  DeliveryAddress? _selectedAddress;
  DeliveryTimeSlot? _selectedTimeSlot;
  PaymentMethod? _selectedPaymentMethod;
  String _orderNotes = '';

  // Order data from cart
  Map<String, dynamic>? _orderData;

  final List<String> _stepTitles = [
    'Address',
    'Delivery',
    'Payment',
    'Review',
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final arguments =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    _orderData = arguments;
  }

  void _nextStep() {
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onAddressSelected(DeliveryAddress address) {
    setState(() {
      _selectedAddress = address;
    });
  }

  void _onTimeSlotSelected(DeliveryTimeSlot timeSlot) {
    setState(() {
      _selectedTimeSlot = timeSlot;
    });
  }

  void _onPaymentMethodSelected(PaymentMethod paymentMethod) {
    setState(() {
      _selectedPaymentMethod = paymentMethod;
    });
  }

  void _onOrderNotesChanged(String notes) {
    setState(() {
      _orderNotes = notes;
    });
  }

  bool _canProceedToNextStep() {
    switch (_currentStep) {
      case 0:
        return _selectedAddress != null;
      case 1:
        return _selectedTimeSlot != null;
      case 2:
        return _selectedPaymentMethod != null;
      case 3:
        return true; // Review step
      default:
        return false;
    }
  }

  Future<void> _placeOrder() async {
    if (!_canProceedToNextStep()) return;

    setState(() {
      _isProcessingOrder = true;
    });

    try {
      // Simulate order processing
      await Future.delayed(const Duration(seconds: 3));

      // Generate order number
      _orderNumber =
          'CM${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';

      setState(() {
        _orderCompleted = true;
        _isProcessingOrder = false;
      });
    } catch (e) {
      setState(() {
        _isProcessingOrder = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Order failed: ${e.toString()}'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_orderCompleted) {
      return OrderSuccessWidget(
        orderNumber: _orderNumber,
        deliveryTime: _selectedTimeSlot?.displayTime ?? 'Unknown',
        onTrackOrder: () {
          Navigator.pushReplacementNamed(context, AppRoutes.orderTracking,
              arguments: _orderNumber);
        },
        onContinueShopping: () {
          Navigator.pushNamedAndRemoveUntil(
            context,
            AppRoutes.home,
            (route) => false,
          );
        },
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Checkout'),
        leading: IconButton(
          onPressed:
              _currentStep > 0 ? _previousStep : () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: List.generate(_stepTitles.length, (index) {
                final isActive = index == _currentStep;
                final isCompleted = index < _currentStep;

                return Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 1.h),
                          decoration: BoxDecoration(
                            color: isActive || isCompleted
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 6.w,
                                    height: 6.w,
                                    decoration: BoxDecoration(
                                      color: isActive || isCompleted
                                          ? Colors.white
                                          : Theme.of(context).disabledColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: isCompleted
                                          ? Icon(
                                              Icons.check,
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              size: 14.sp,
                                            )
                                          : Text(
                                              '${index + 1}',
                                              style: TextStyle(
                                                color: isActive
                                                    ? Theme.of(context)
                                                        .primaryColor
                                                    : Colors.grey[600],
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12.sp,
                                              ),
                                            ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 0.5.h),
                              Text(
                                _stepTitles[index],
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: isActive || isCompleted
                                          ? Colors.white
                                          : Theme.of(context).disabledColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (index < _stepTitles.length - 1)
                        Container(
                          width: 2.w,
                          height: 0.2.h,
                          color: index < _currentStep
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).dividerColor,
                        ),
                    ],
                  ),
                );
              }),
            ),
          ),

          // Step content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Step 1: Delivery Address
                DeliveryAddressWidget(
                  selectedAddress: _selectedAddress,
                  onAddressSelected: _onAddressSelected,
                ),

                // Step 2: Delivery Time
                DeliveryTimeWidget(
                  selectedTimeSlot: _selectedTimeSlot,
                  onTimeSlotSelected: _onTimeSlotSelected,
                ),

                // Step 3: Payment Method
                PaymentMethodWidget(
                  selectedPaymentMethod: _selectedPaymentMethod,
                  onPaymentMethodSelected: _onPaymentMethodSelected,
                ),

                // Step 4: Order Review
                CheckoutOrderSummaryWidget(
                  orderData: _orderData,
                  selectedAddress: _selectedAddress,
                  selectedTimeSlot: _selectedTimeSlot,
                  selectedPaymentMethod: _selectedPaymentMethod,
                  orderNotes: _orderNotes,
                  onOrderNotesChanged: _onOrderNotesChanged,
                ),
              ],
            ),
          ),

          // Bottom action button
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              top: false,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Total amount
                  if (_orderData != null) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total Amount',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                        Text(
                          'KES ${_orderData!['total']?.toStringAsFixed(0) ?? '0'}',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                  ],

                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isProcessingOrder
                          ? null
                          : _currentStep == _stepTitles.length - 1
                              ? _placeOrder
                              : _canProceedToNextStep()
                                  ? _nextStep
                                  : null,
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 2.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isProcessingOrder
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 5.w,
                                  height: 5.w,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color:
                                        Theme.of(context).colorScheme.onPrimary,
                                  ),
                                ),
                                SizedBox(width: 3.w),
                                Text(
                                  'Processing Order...',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            )
                          : Text(
                              _currentStep == _stepTitles.length - 1
                                  ? 'Place Order'
                                  : 'Continue',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    color:
                                        Theme.of(context).colorScheme.onPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

// Data models
class DeliveryAddress {
  final String id;
  final String label;
  final String fullAddress;
  final String coordinates;
  final bool isDefault;

  const DeliveryAddress({
    required this.id,
    required this.label,
    required this.fullAddress,
    required this.coordinates,
    required this.isDefault,
  });
}

class DeliveryTimeSlot {
  final String id;
  final String displayTime;
  final String date;
  final double fee;
  final bool isRecommended;
  final bool isAvailable;

  const DeliveryTimeSlot({
    required this.id,
    required this.displayTime,
    required this.date,
    required this.fee,
    required this.isRecommended,
    required this.isAvailable,
  });
}

class PaymentMethod {
  final String id;
  final String name;
  final String type; // 'card', 'mobile_money', 'cod'
  final String? lastFourDigits;
  final String? provider;
  final IconData icon;
  final bool isDefault;

  const PaymentMethod({
    required this.id,
    required this.name,
    required this.type,
    this.lastFourDigits,
    this.provider,
    required this.icon,
    required this.isDefault,
  });
}
