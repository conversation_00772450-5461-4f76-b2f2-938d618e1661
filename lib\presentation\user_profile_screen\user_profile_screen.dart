import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';


import '../../core/app_export.dart';

import './widgets/profile_header_widget.dart';
import './widgets/profile_menu_widget.dart';
import './widgets/profile_stats_widget.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({Key? key}) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final AuthService _authService = AuthService.instance;
  UserProfile? _userProfile;


  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserProfile();
    _animationController.forward();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
  }

  void _loadUserProfile() {
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      _userProfile = UserProfile(
        id: currentUser.id,
        name: currentUser.fullName,
        email: currentUser.email,
        phone: currentUser.phone ?? '+254 712 345 678',
        profileImage: currentUser.profileImageUrl ??
            'https://images.unsplash.com/photo-1494790108755-2616b612b742?w=150',
        joinDate: currentUser.createdAt,
        totalOrders: 47, // This would come from orders API
        totalSpent: 12750.0, // This would come from orders API
        favoriteItems: 23, // This would come from favorites API
        isManager: currentUser.canManageInventory,
        addresses: [
          UserAddress(
            id: '1',
            label: 'Home',
            fullAddress: '123 Westlands Road, Westlands, Nairobi',
            isDefault: true,
          ),
          UserAddress(
            id: '2',
            label: 'Office',
            fullAddress: '456 Kenyatta Avenue, CBD, Nairobi',
            isDefault: false,
          ),
        ],
        preferences: UserPreferences(
          notifications: true,
          emailUpdates: true,
          promotions: true,
          darkMode: false,
          language: 'English',
        ),
      );
    } else {
      // Fallback mock data if no user is logged in
      _userProfile = UserProfile(
        id: '1',
        name: 'Guest User',
        email: '<EMAIL>',
        phone: '+254 712 345 678',
        profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b742?w=150',
        joinDate: DateTime.now().subtract(const Duration(days: 365)),
        totalOrders: 0,
        totalSpent: 0.0,
        favoriteItems: 0,
        isManager: false,
        addresses: [],
        preferences: UserPreferences(
          notifications: true,
          emailUpdates: true,
          promotions: true,
          darkMode: false,
          language: 'English',
        ),
      );
    }
    setState(() {});
  }

  Future<void> _refreshProfile() async {
    await Future.delayed(const Duration(seconds: 1));
    _loadUserProfile();
  }

  void _navigateToEditProfile() {
    // In real app, navigate to edit profile screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit Profile feature coming soon')),
    );
  }

  void _navigateToOrders() {
    // In real app, navigate to order history screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Order History feature coming soon')),
    );
  }

  void _navigateToAddresses() {
    // In real app, navigate to manage addresses screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Manage Addresses feature coming soon')),
    );
  }

  void _navigateToPaymentMethods() {
    // In real app, navigate to payment methods screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Payment Methods feature coming soon')),
    );
  }

  void _navigateToNotificationSettings() {
    // In real app, navigate to notification settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification Settings feature coming soon'),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _performLogout();
                },
                child: const Text('Logout'),
              ),
            ],
          ),
    );
  }

  Future<void> _performLogout() async {
    try {
      await _authService.logout();
      if (mounted) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          AppRoutes.login,
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Logout failed: $e')),
        );
      }
    }
  }

  void _navigateToManagerDashboard() {
    Navigator.pushNamed(context, AppRoutes.managerDashboard);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body:
          _userProfile == null
              ? const Center(child: CircularProgressIndicator())
              : FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: RefreshIndicator(
                    onRefresh: _refreshProfile,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(
                        children: [
                          // Profile Header
                          ProfileHeaderWidget(
                            userProfile: _userProfile!,
                            onEditProfile: _navigateToEditProfile,
                          ),

                          SizedBox(height: 2.h),

                          // Profile Stats
                          ProfileStatsWidget(userProfile: _userProfile!),

                          SizedBox(height: 3.h),

                          // Profile Menu
                          ProfileMenuWidget(
                            userProfile: _userProfile!,
                            onOrderHistory: _navigateToOrders,
                            onAddresses: _navigateToAddresses,
                            onPaymentMethods: _navigateToPaymentMethods,
                            onNotifications: _navigateToNotificationSettings,
                            onManagerDashboard: _navigateToManagerDashboard,
                            onLogout: _showLogoutDialog,
                          ),

                          SizedBox(height: 2.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

// Data Models
class UserProfile {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String profileImage;
  final DateTime joinDate;
  final int totalOrders;
  final double totalSpent;
  final int favoriteItems;
  final bool isManager;
  final List<UserAddress> addresses;
  final UserPreferences preferences;

  const UserProfile({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.profileImage,
    required this.joinDate,
    required this.totalOrders,
    required this.totalSpent,
    required this.favoriteItems,
    required this.isManager,
    required this.addresses,
    required this.preferences,
  });
}

class UserAddress {
  final String id;
  final String label;
  final String fullAddress;
  final bool isDefault;

  const UserAddress({
    required this.id,
    required this.label,
    required this.fullAddress,
    required this.isDefault,
  });
}

class UserPreferences {
  final bool notifications;
  final bool emailUpdates;
  final bool promotions;
  final bool darkMode;
  final String language;

  const UserPreferences({
    required this.notifications,
    required this.emailUpdates,
    required this.promotions,
    required this.darkMode,
    required this.language,
  });
}
