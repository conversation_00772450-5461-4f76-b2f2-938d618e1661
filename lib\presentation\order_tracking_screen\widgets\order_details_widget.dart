import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../order_tracking_screen.dart';

class OrderDetailsWidget extends StatelessWidget {
  final OrderInfo orderInfo;
  final VoidCallback? onReorder;

  const OrderDetailsWidget({Key? key, required this.orderInfo, this.onReorder})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order Details',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton.icon(
                onPressed: onReorder,
                icon: Icon(Icons.refresh, size: 16.sp),
                label: const Text('Reorder'),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),

          // Items list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: orderInfo.items.length,
            separatorBuilder: (context, index) => SizedBox(height: 2.h),
            itemBuilder: (context, index) {
              final item = orderInfo.items[index];
              return Row(
                children: [
                  // Item image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: item.imageUrl,
                      width: 12.w,
                      height: 12.w,
                      fit: BoxFit.cover,
                      placeholder:
                          (context, url) => Container(
                            width: 12.w,
                            height: 12.w,
                            color: Theme.of(context).dividerColor,
                            child: Icon(
                              Icons.image,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                              size: 16.sp,
                            ),
                          ),
                      errorWidget:
                          (context, url, error) => Container(
                            width: 12.w,
                            height: 12.w,
                            color: Theme.of(context).dividerColor,
                            child: Icon(
                              Icons.image,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                              size: 16.sp,
                            ),
                          ),
                    ),
                  ),
                  SizedBox(width: 3.w),

                  // Item details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        SizedBox(height: 0.5.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Qty: ${item.quantity}',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color:
                                    Theme.of(
                                      context,
                                    ).textTheme.bodySmall?.color,
                              ),
                            ),
                            Text(
                              'KES ${(item.price * item.quantity).toStringAsFixed(0)}',
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),

          SizedBox(height: 3.h),
          Divider(color: Theme.of(context).dividerColor),
          SizedBox(height: 2.h),

          // Order summary
          _buildSummaryRow(
            context,
            'Subtotal',
            'KES ${(orderInfo.total * 0.85).toStringAsFixed(0)}',
            isTotal: false,
          ),
          SizedBox(height: 1.h),
          _buildSummaryRow(context, 'Delivery Fee', 'KES 150', isTotal: false),
          SizedBox(height: 1.h),
          _buildSummaryRow(
            context,
            'Taxes (16%)',
            'KES ${(orderInfo.total * 0.136).toStringAsFixed(0)}',
            isTotal: false,
          ),
          SizedBox(height: 2.h),
          _buildSummaryRow(
            context,
            'Total',
            'KES ${orderInfo.total.toStringAsFixed(0)}',
            isTotal: true,
          ),

          SizedBox(height: 3.h),

          // Order actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    _showOrderHelp(context);
                  },
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Need Help?'),
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: onReorder,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Reorder Items'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    required bool isTotal,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style:
              isTotal
                  ? Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)
                  : Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
        ),
        Text(
          value,
          style:
              isTotal
                  ? Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  )
                  : Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  void _showOrderHelp(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: 50.h,
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  width: 10.w,
                  height: 0.5.h,
                  margin: EdgeInsets.only(top: 2.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Need Help?',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 3.h),

                      // Help options
                      _buildHelpOption(
                        context,
                        Icons.chat,
                        'Chat with Support',
                        'Get instant help from our support team',
                        () => Navigator.pop(context),
                      ),
                      SizedBox(height: 2.h),
                      _buildHelpOption(
                        context,
                        Icons.phone,
                        'Call Customer Service',
                        'Speak directly with our customer service',
                        () => Navigator.pop(context),
                      ),
                      SizedBox(height: 2.h),
                      _buildHelpOption(
                        context,
                        Icons.cancel,
                        'Cancel Order',
                        'Cancel this order (if eligible)',
                        () => Navigator.pop(context),
                      ),
                      SizedBox(height: 2.h),
                      _buildHelpOption(
                        context,
                        Icons.report_problem,
                        'Report Issue',
                        'Report a problem with your order',
                        () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildHelpOption(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor, width: 1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 20.sp,
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).textTheme.bodySmall?.color,
              size: 16.sp,
            ),
          ],
        ),
      ),
    );
  }
}
