import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FilterModalWidget extends StatefulWidget {
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onFiltersApplied;

  const FilterModalWidget({
    Key? key,
    required this.currentFilters,
    required this.onFiltersApplied,
  }) : super(key: key);

  @override
  State<FilterModalWidget> createState() => _FilterModalWidgetState();
}

class _FilterModalWidgetState extends State<FilterModalWidget> {
  late Map<String, dynamic> _filters;

  @override
  void initState() {
    super.initState();
    _filters = Map<String, dynamic>.from(widget.currentFilters);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70.h,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle Bar
          Container(
            margin: EdgeInsets.only(top: 1.h),
            width: 10.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Categories',
                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: CustomIconWidget(
                    iconName: 'close',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          Divider(color: AppTheme.lightTheme.colorScheme.outline),

          // Filter Options
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Availability Filter
                  _buildFilterSection(
                    'Availability',
                    [
                      _buildCheckboxOption(
                          'Available for delivery', 'available'),
                      _buildCheckboxOption('Limited stock', 'limited'),
                    ],
                  ),

                  SizedBox(height: 3.h),

                  // Price Range Filter
                  _buildFilterSection(
                    'Price Range',
                    [
                      _buildCheckboxOption('Under KES 500', 'under_500'),
                      _buildCheckboxOption('KES 500 - 1,000', '500_1000'),
                      _buildCheckboxOption('KES 1,000 - 2,000', '1000_2000'),
                      _buildCheckboxOption('Above KES 2,000', 'above_2000'),
                    ],
                  ),

                  SizedBox(height: 3.h),

                  // Dietary Preferences
                  _buildFilterSection(
                    'Dietary Preferences',
                    [
                      _buildCheckboxOption('Vegetarian', 'vegetarian'),
                      _buildCheckboxOption('Organic', 'organic'),
                      _buildCheckboxOption('Gluten-free', 'gluten_free'),
                      _buildCheckboxOption('Dairy-free', 'dairy_free'),
                    ],
                  ),

                  SizedBox(height: 3.h),

                  // Brands Filter
                  _buildFilterSection(
                    'Popular Brands',
                    [
                      _buildCheckboxOption('Brookside', 'brookside'),
                      _buildCheckboxOption('Farmers Choice', 'farmers_choice'),
                      _buildCheckboxOption('Ketepa', 'ketepa'),
                      _buildCheckboxOption('Bidco', 'bidco'),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.outline,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                // Clear Filters
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _filters.clear();
                      });
                    },
                    child: const Text('Clear All'),
                  ),
                ),

                SizedBox(width: 4.w),

                // Apply Filters
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onFiltersApplied(_filters);
                      Navigator.pop(context);
                    },
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection(String title, List<Widget> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.lightTheme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 1.h),
        ...options,
      ],
    );
  }

  Widget _buildCheckboxOption(String label, String key) {
    final bool isSelected = _filters[key] == true;

    return GestureDetector(
      onTap: () {
        setState(() {
          _filters[key] = !isSelected;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h),
        child: Row(
          children: [
            Container(
              width: 5.w,
              height: 5.w,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.lightTheme.colorScheme.primary
                    : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? AppTheme.lightTheme.colorScheme.primary
                      : AppTheme.lightTheme.colorScheme.outline,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: isSelected
                  ? CustomIconWidget(
                      iconName: 'check',
                      color: AppTheme.lightTheme.colorScheme.onPrimary,
                      size: 12,
                    )
                  : null,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                label,
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
