import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class OrderSuccessWidget extends StatelessWidget {
  final String orderNumber;
  final String deliveryTime;
  final VoidCallback onTrackOrder;
  final VoidCallback onContinueShopping;

  const OrderSuccessWidget({
    Key? key,
    required this.orderNumber,
    required this.deliveryTime,
    required this.onTrackOrder,
    required this.onContinueShopping,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(6.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Success animation/icon
              Container(
                width: 30.w,
                height: 30.w,
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 15.w,
                ),
              ),

              SizedBox(height: 4.h),

              // Success title
              Text(
                'Order Placed Successfully!',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 2.h),

              // Order number
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withAlpha(77),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Order Number',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).textTheme.bodySmall!.color,
                          ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      orderNumber,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                            letterSpacing: 2,
                          ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 3.h),

              // Delivery information
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withAlpha(13),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withAlpha(51),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          color: Theme.of(context).primaryColor,
                          size: 20.sp,
                        ),
                        SizedBox(width: 3.w),
                        Text(
                          'Estimated Delivery',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ],
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      deliveryTime,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    SizedBox(height: 2.h),
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).primaryColor,
                          size: 16.sp,
                        ),
                        SizedBox(width: 2.w),
                        Expanded(
                          child: Text(
                            'We\'ll call you 15 minutes before delivery',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context).primaryColor,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SizedBox(height: 4.h),

              // Track order button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onTrackOrder,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.track_changes,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 20.sp,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        'Track Your Order',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 2.h),

              // Continue shopping button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: onContinueShopping,
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Continue Shopping',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),

              SizedBox(height: 4.h),

              // Additional information
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildInfoItem(
                          context,
                          Icons.headset_mic,
                          '24/7 Support',
                          'Customer care',
                        ),
                        Container(
                          width: 1,
                          height: 6.h,
                          color: Theme.of(context).dividerColor,
                        ),
                        _buildInfoItem(
                          context,
                          Icons.refresh,
                          'Easy Returns',
                          'Within 24 hours',
                        ),
                        Container(
                          width: 1,
                          height: 6.h,
                          color: Theme.of(context).dividerColor,
                        ),
                        _buildInfoItem(
                          context,
                          Icons.security,
                          'Secure Payment',
                          'Protected',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
  ) {
    return Expanded(
      child: Column(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 20.sp,
          ),
          SizedBox(height: 1.h),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 0.5.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
