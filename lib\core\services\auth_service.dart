import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user_model.dart';
import '../repositories/user_repository.dart';
import 'api_service.dart';

class AuthService {
  static AuthService? _instance;
  static const String _userKey = 'current_user';
  static const String _tokenKey = 'auth_token';
  
  final UserRepository _userRepository = UserRepository();
  final ApiService _apiService = ApiService.instance;
  UserModel? _currentUser;
  String? _authToken;
  
  AuthService._internal();
  
  static AuthService get instance {
    _instance ??= AuthService._internal();
    return _instance!;
  }
  
  /// Get current authenticated user
  UserModel? get currentUser => _currentUser;
  
  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;
  
  /// Initialize auth service and check for existing session
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      final token = prefs.getString(_tokenKey);

      if (userJson != null && token != null) {
        final userMap = json.decode(userJson);
        _currentUser = UserModel.fromMap(userMap);
        _authToken = token;

        // Set the auth token in API service
        _apiService.setAuthToken(token);
      }
    } catch (e) {
      // Clear invalid session data
      await clearSession();
    }
  }
  
  /// Login user
  Future<UserModel?> login({
    required String email,
    required String password,
  }) async {
    try {
      final user = await _userRepository.loginUser(
        email: email,
        password: password,
      );

      if (user != null) {
        // Get the token from UserRepository (set during login)
        _authToken = _userRepository.currentToken;
        await _saveUserSession(user, _authToken);
        _currentUser = user;
      }

      return user;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Register new user
  Future<UserModel?> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final user = await _userRepository.registerUser(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      // Get the token from UserRepository (set during registration)
      _authToken = _userRepository.currentToken;
      await _saveUserSession(user, _authToken);
      _currentUser = user;

      return user;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Logout user
  Future<void> logout() async {
    // Clear auth token from API service and repository
    _apiService.clearAuthToken();
    _userRepository.clearToken();

    await clearSession();
    _currentUser = null;
    _authToken = null;
  }
  
  /// Update user profile
  Future<UserModel?> updateProfile(UserModel updatedUser) async {
    try {
      final user = await _userRepository.updateUser(updatedUser);
      await _saveUserSession(user, _authToken);
      _currentUser = user;
      return user;
    } catch (e) {
      rethrow;
    }
  }
  
  /// Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) {
      throw Exception('User not authenticated');
    }
    
    try {
      return await _userRepository.changePassword(
        userId: _currentUser!.id,
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
    } catch (e) {
      rethrow;
    }
  }
  
  /// Save user session to local storage
  Future<void> _saveUserSession(UserModel user, [String? token]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = json.encode(user.toMap());
      await prefs.setString(_userKey, userJson);

      // Use provided token or generate a simple session token
      final sessionToken = token ?? 'session_${user.id}_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(_tokenKey, sessionToken);
      _authToken = sessionToken;
    } catch (e) {
      throw Exception('Failed to save user session: $e');
    }
  }
  
  /// Clear user session
  Future<void> clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      await prefs.remove(_tokenKey);
    } catch (e) {
      // Ignore errors when clearing session
    }
  }
  
  /// Get session token
  Future<String?> getSessionToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      return null;
    }
  }
  
  /// Refresh user data from database
  Future<void> refreshUser() async {
    if (_currentUser == null) return;
    
    try {
      final user = await _userRepository.getUserById(_currentUser!.id);
      if (user != null) {
        await _saveUserSession(user);
        _currentUser = user;
      }
    } catch (e) {
      // Handle refresh error silently
    }
  }
}
