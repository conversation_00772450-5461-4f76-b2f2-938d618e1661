import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../theme/app_theme.dart';
import './widgets/customer_reviews_widget.dart';
import './widgets/product_description_widget.dart';
import './widgets/product_image_carousel_widget.dart';
import './widgets/product_info_widget.dart';
import './widgets/quantity_selector_widget.dart';
import './widgets/similar_products_widget.dart';
import './widgets/sticky_bottom_bar_widget.dart';

class ProductDetailScreen extends StatefulWidget {
  const ProductDetailScreen({Key? key}) : super(key: key);

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen>
    with TickerProviderStateMixin {
  int _selectedQuantity = 1;
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  // Mock product data
  final Map<String, dynamic> _productData = {
    "id": 1,
    "name": "Fresh Organic Bananas",
    "price": "120.00",
    "originalPrice": "150.00",
    "discount": 20,
    "rating": 4.5,
    "reviewCount": 128,
    "inStock": true,
    "maxQuantity": 10,
    "description":
        """Fresh organic bananas sourced directly from local farms in Kenya. These premium quality bananas are naturally ripened and packed with essential nutrients including potassium, vitamin B6, and dietary fiber. Perfect for healthy snacking, smoothies, or baking. Our bananas are carefully selected to ensure optimal freshness and taste.""",
    "ingredients":
        "100% Organic Bananas - No artificial preservatives, colors, or flavors added. Naturally grown without pesticides or chemical fertilizers.",
    "nutrition": """Per 100g serving:
• Calories: 89
• Carbohydrates: 23g
• Dietary Fiber: 2.6g
• Sugars: 12g
• Protein: 1.1g
• Fat: 0.3g
• Potassium: 358mg
• Vitamin B6: 0.4mg
• Vitamin C: 8.7mg
• Magnesium: 27mg""",
    "storage":
        "Store at room temperature until ripe. Once ripe, refrigerate to extend freshness for up to 5-7 days. Keep away from direct sunlight and heat sources. For longer storage, peel and freeze ripe bananas for up to 3 months.",
    "images": [
      "https://images.pexels.com/photos/2872755/pexels-photo-2872755.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "https://images.pexels.com/photos/5966630/pexels-photo-5966630.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "https://images.pexels.com/photos/4021779/pexels-photo-4021779.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    ],
  };

  // Mock reviews data
  final List<Map<String, dynamic>> _reviewsData = [
    {
      "id": 1,
      "userName": "Sarah Wanjiku",
      "userAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "rating": 5,
      "date": "2 days ago",
      "comment":
          "Excellent quality bananas! They arrived perfectly ripe and fresh. Great for my morning smoothies. Will definitely order again.",
      "images": [
        "https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=400",
      ],
    },
    {
      "id": 2,
      "userName": "John Kamau",
      "userAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "rating": 4,
      "date": "1 week ago",
      "comment":
          "Good quality organic bananas. Delivery was quick and packaging was excellent. Slightly expensive but worth it for organic quality.",
      "images": [],
    },
    {
      "id": 3,
      "userName": "Grace Njeri",
      "userAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "rating": 5,
      "date": "2 weeks ago",
      "comment":
          "Best bananas I've bought online! Sweet, fresh, and perfectly ripe. My kids love them. ChuiMart never disappoints with their fresh produce.",
      "images": [
        "https://images.pexels.com/photos/4021779/pexels-photo-4021779.jpeg?auto=compress&cs=tinysrgb&w=400",
        "https://images.pexels.com/photos/5966630/pexels-photo-5966630.jpeg?auto=compress&cs=tinysrgb&w=400",
      ],
    },
    {
      "id": 4,
      "userName": "Michael Ochieng",
      "userAvatar":
          "https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_640.png",
      "rating": 4,
      "date": "3 weeks ago",
      "comment":
          "Fresh and tasty bananas. Good for the price. Delivery was on time and the bananas were well packaged to prevent bruising.",
      "images": [],
    },
  ];

  // Mock similar products data
  final List<Map<String, dynamic>> _similarProductsData = [
    {
      "id": 2,
      "name": "Organic Green Apples",
      "price": "180.00",
      "originalPrice": "220.00",
      "discount": 18,
      "rating": 4.3,
      "image":
          "https://images.pexels.com/photos/102104/pexels-photo-102104.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      "id": 3,
      "name": "Fresh Oranges",
      "price": "150.00",
      "rating": 4.6,
      "image":
          "https://images.pexels.com/photos/161559/background-bitter-breakfast-bright-161559.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      "id": 4,
      "name": "Ripe Mangoes",
      "price": "200.00",
      "originalPrice": "250.00",
      "discount": 20,
      "rating": 4.8,
      "image":
          "https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
    {
      "id": 5,
      "name": "Fresh Pineapple",
      "price": "300.00",
      "rating": 4.4,
      "image":
          "https://images.pexels.com/photos/947879/pexels-photo-947879.jpeg?auto=compress&cs=tinysrgb&w=400",
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleQuantityChange(int quantity) {
    setState(() {
      _selectedQuantity = quantity;
    });
  }

  void _handleAddToCart() async {
    if (!(_productData['inStock'] as bool)) return;

    setState(() {
      _isLoading = true;
    });

    // Animate button press
    await _animationController.forward();
    await _animationController.reverse();

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _isLoading = false;
    });

    // Show success message
    Fluttertoast.showToast(
      msg: "Added $_selectedQuantity ${_productData['name']} to cart!",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.lightTheme.colorScheme.tertiary,
      textColor: Colors.white,
    );

    // Success haptic feedback
    HapticFeedback.mediumImpact();
  }

  void _handleAddToWishlist() {
    Fluttertoast.showToast(
      msg: "Added to wishlist!",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.lightTheme.primaryColor,
      textColor: Colors.white,
    );
    HapticFeedback.lightImpact();
  }

  void _handleShare() {
    Fluttertoast.showToast(
      msg: "Sharing ${_productData['name']}...",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: Stack(
        children: [
          CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: ProductImageCarouselWidget(
                  images: (_productData['images'] as List).cast<String>(),
                  heroTag: 'product_${_productData['id']}',
                ),
              ),
              SliverToBoxAdapter(
                child: ProductInfoWidget(
                  product: _productData,
                ),
              ),
              if (_productData['inStock'] as bool)
                SliverToBoxAdapter(
                  child: QuantitySelectorWidget(
                    initialQuantity: _selectedQuantity,
                    maxQuantity: _productData['maxQuantity'] as int,
                    onQuantityChanged: _handleQuantityChange,
                  ),
                ),
              SliverToBoxAdapter(
                child: ProductDescriptionWidget(
                  product: _productData,
                ),
              ),
              SliverToBoxAdapter(
                child: CustomerReviewsWidget(
                  reviews: _reviewsData,
                  averageRating: _productData['rating'] as double,
                  totalReviews: _productData['reviewCount'] as int,
                ),
              ),
              SliverToBoxAdapter(
                child: SimilarProductsWidget(
                  similarProducts: _similarProductsData,
                ),
              ),
              SliverToBoxAdapter(
                child: SizedBox(height: 20.h), // Space for sticky bottom bar
              ),
            ],
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: StickyBottomBarWidget(
                    product: _productData,
                    quantity: _selectedQuantity,
                    onQuantityChanged: _handleQuantityChange,
                    onAddToCart: _isLoading ? () {} : _handleAddToCart,
                    onAddToWishlist: _handleAddToWishlist,
                    onShare: _handleShare,
                  ),
                );
              },
            ),
          ),
          if (_isLoading)
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.3),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(4.w),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          color: AppTheme.lightTheme.primaryColor,
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          'Adding to cart...',
                          style: AppTheme.lightTheme.textTheme.bodyMedium
                              ?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
