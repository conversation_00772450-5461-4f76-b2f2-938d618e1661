import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/app_export.dart';


class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({Key? key}) : super(key: key);

  @override
  State<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  final ApiTestService _testService = ApiTestService.instance;
  bool _isLoading = false;
  Map<String, dynamic>? _testResults;
  String _healthSummary = '';

  @override
  void initState() {
    super.initState();
    _runTests();
  }

  Future<void> _runTests() async {
    setState(() {
      _isLoading = true;
      _testResults = null;
      _healthSummary = '';
    });

    try {
      final results = await _testService.testAllEndpoints();
      final summary = await _testService.getApiHealthSummary();
      
      setState(() {
        _testResults = results;
        _healthSummary = summary;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _healthSummary = '❌ Test execution failed: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'API Test Results',
          style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runTests,
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Health Summary Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(4.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'API Health Status',
                        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      if (_isLoading)
                        const Row(
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 12),
                            Text('Running API tests...'),
                          ],
                        )
                      else
                        Text(
                          _healthSummary,
                          style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 3.h),
              
              // Test Results
              if (_testResults != null) ...[
                Text(
                  'Detailed Test Results',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2.h),
                
                Expanded(
                  child: ListView(
                    children: _testResults!.entries.map((entry) {
                      return _buildTestResultCard(entry.key, entry.value);
                    }).toList(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestResultCard(String testName, dynamic result) {
    final isSuccess = result['success'] == true;
    final icon = isSuccess ? Icons.check_circle : Icons.error;
    final iconColor = isSuccess ? Colors.green : Colors.red;
    
    return Card(
      margin: EdgeInsets.only(bottom: 2.h),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ExpansionTile(
        leading: Icon(icon, color: iconColor),
        title: Text(
          _formatTestName(testName),
          style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          result['message'] ?? (isSuccess ? 'Success' : 'Failed'),
          style: AppTheme.lightTheme.textTheme.bodySmall,
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (result['error'] != null) ...[
                  Text(
                    'Error:',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    result['error'].toString(),
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: Colors.red[700],
                    ),
                  ),
                ],
                
                if (result['tables'] != null) ...[
                  Text(
                    'Database Tables:',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  ...((result['tables'] as Map<String, dynamic>).entries.map((entry) {
                    final exists = entry.value == true;
                    return Row(
                      children: [
                        Icon(
                          exists ? Icons.check : Icons.close,
                          size: 16,
                          color: exists ? Colors.green : Colors.red,
                        ),
                        SizedBox(width: 2.w),
                        Text(entry.key),
                      ],
                    );
                  })),
                ],
                
                if (result['count'] != null) ...[
                  Text(
                    'Count: ${result['count']}',
                    style: AppTheme.lightTheme.textTheme.bodyMedium,
                  ),
                ],
                
                if (result['user_id'] != null) ...[
                  Text(
                    'User ID: ${result['user_id']}',
                    style: AppTheme.lightTheme.textTheme.bodySmall,
                  ),
                ],
                
                if (result['categories'] != null || result['products'] != null) ...[
                  SizedBox(height: 1.h),
                  Text(
                    'Sample Data:',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    (result['categories'] ?? result['products']).toString(),
                    style: AppTheme.lightTheme.textTheme.bodySmall,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTestName(String testName) {
    switch (testName) {
      case 'connectivity':
        return 'API Connectivity';
      case 'schema':
        return 'Database Schema';
      case 'registration':
        return 'User Registration';
      case 'login':
        return 'User Login';
      case 'categories':
        return 'Categories Endpoint';
      case 'products':
        return 'Products Endpoint';
      case 'cart':
        return 'Cart Operations';
      default:
        return testName.toUpperCase();
    }
  }
}
