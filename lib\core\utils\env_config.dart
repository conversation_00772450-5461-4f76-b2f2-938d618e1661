import 'dart:convert';
import 'package:flutter/services.dart';

class EnvConfig {
  static Map<String, dynamic>? _config;
  
  /// Load environment configuration from env.json
  static Future<void> load() async {
    if (_config != null) return;
    
    try {
      final configString = await rootBundle.loadString('assets/env.json');
      _config = json.decode(configString);
    } catch (e) {
      throw Exception('Failed to load environment configuration: $e');
    }
  }
  
  /// Get database URL
  static String get databaseUrl {
    _ensureLoaded();
    return _config!['DATABASE_URL'] ?? '';
  }
  
  /// Get API base URL
  static String get apiBaseUrl {
    _ensureLoaded();
    return _config!['API_BASE_URL'] ?? 'http://localhost:3000/api';
  }

  /// Get REST API endpoint for Neon Auth
  static String get restApiEndpoint {
    _ensureLoaded();
    return _config!['REST_API_ENDPOINT'] ?? '';
  }

  /// Get Stack Auth Project ID
  static String get stackAuthProjectId {
    _ensureLoaded();
    return _config!['STACK_AUTH_PROJECT_ID'] ?? '';
  }

  /// Get JWKS URL
  static String get jwksUrl {
    _ensureLoaded();
    return _config!['JWKS_URL'] ?? '';
  }
  
  /// Ensure configuration is loaded
  static void _ensureLoaded() {
    if (_config == null) {
      throw Exception('Environment configuration not loaded. Call EnvConfig.load() first.');
    }
  }
  
  /// Check if configuration is loaded
  static bool get isLoaded => _config != null;
}
