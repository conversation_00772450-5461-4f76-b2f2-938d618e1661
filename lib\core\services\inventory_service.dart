import '../repositories/inventory_repository.dart';
import '../repositories/product_repository.dart';
import '../models/inventory_transaction_model.dart';
import '../models/stock_alert_model.dart';
import '../models/product_model.dart';
import 'auth_service.dart';

class InventoryService {
  static InventoryService? _instance;
  final InventoryRepository _inventoryRepository = InventoryRepository();
  final ProductRepository _productRepository = ProductRepository();
  final AuthService _authService = AuthService.instance;

  InventoryService._internal();

  static InventoryService get instance {
    _instance ??= InventoryService._internal();
    return _instance!;
  }

  /// Check if current user can manage inventory
  bool get canManageInventory {
    final user = _authService.currentUser;
    return user?.canManageInventory ?? false;
  }

  /// Update product stock by SKU
  Future<bool> updateStockBySku({
    required String sku,
    required double newStock,
    String? notes,
  }) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to manage inventory');
    }

    try {
      // Get product by SKU
      final product = await _productRepository.getProductBySku(sku);
      if (product == null) {
        throw Exception('Product with SKU $sku not found');
      }

      return await _inventoryRepository.updateProductStock(
        productId: product.id,
        newStock: newStock,
        transactionType: TransactionType.adjustment,
        notes: notes,
        userId: _authService.currentUser?.id,
      );
    } catch (e) {
      throw Exception('Failed to update stock: $e');
    }
  }

  /// Add stock to product
  Future<bool> addStock({
    required String productId,
    required double quantity,
    String? referenceId,
    String? notes,
  }) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to manage inventory');
    }

    try {
      final product = await _productRepository.getProductById(productId);
      if (product == null) {
        throw Exception('Product not found');
      }

      final newStock = product.stock + quantity;

      return await _inventoryRepository.updateProductStock(
        productId: productId,
        newStock: newStock,
        transactionType: TransactionType.stockIn,
        referenceId: referenceId,
        notes: notes,
        userId: _authService.currentUser?.id,
      );
    } catch (e) {
      throw Exception('Failed to add stock: $e');
    }
  }

  /// Remove stock from product
  Future<bool> removeStock({
    required String productId,
    required double quantity,
    String? referenceId,
    String? notes,
  }) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to manage inventory');
    }

    try {
      final product = await _productRepository.getProductById(productId);
      if (product == null) {
        throw Exception('Product not found');
      }

      final newStock = (product.stock - quantity).clamp(0.0, double.infinity);

      return await _inventoryRepository.updateProductStock(
        productId: productId,
        newStock: newStock,
        transactionType: TransactionType.stockOut,
        referenceId: referenceId,
        notes: notes,
        userId: _authService.currentUser?.id,
      );
    } catch (e) {
      throw Exception('Failed to remove stock: $e');
    }
  }

  /// Adjust stock to specific amount
  Future<bool> adjustStock({
    required String productId,
    required double newStock,
    String? notes,
  }) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to manage inventory');
    }

    try {
      return await _inventoryRepository.updateProductStock(
        productId: productId,
        newStock: newStock,
        transactionType: TransactionType.adjustment,
        notes: notes,
        userId: _authService.currentUser?.id,
      );
    } catch (e) {
      throw Exception('Failed to adjust stock: $e');
    }
  }

  /// Get inventory transactions for a product
  Future<List<InventoryTransactionModel>> getProductTransactions(
    String productId, {
    int limit = 50,
    int offset = 0,
  }) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to view inventory transactions');
    }

    try {
      return await _inventoryRepository.getProductTransactions(
        productId,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      throw Exception('Failed to get product transactions: $e');
    }
  }

  /// Get all inventory transactions
  Future<List<InventoryTransactionModel>> getAllTransactions({
    int limit = 100,
    int offset = 0,
    TransactionType? filterType,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to view inventory transactions');
    }

    try {
      return await _inventoryRepository.getAllTransactions(
        limit: limit,
        offset: offset,
        filterType: filterType,
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get transactions: $e');
    }
  }

  /// Get active stock alerts
  Future<List<StockAlertModel>> getActiveStockAlerts() async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to view stock alerts');
    }

    try {
      return await _inventoryRepository.getActiveStockAlerts();
    } catch (e) {
      throw Exception('Failed to get stock alerts: $e');
    }
  }

  /// Resolve stock alert
  Future<bool> resolveStockAlert(String alertId) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to resolve stock alerts');
    }

    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      return await _inventoryRepository.resolveStockAlert(alertId, userId);
    } catch (e) {
      throw Exception('Failed to resolve stock alert: $e');
    }
  }

  /// Get products with low stock
  Future<List<ProductModel>> getLowStockProducts() async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to view low stock products');
    }

    try {
      return await _inventoryRepository.getLowStockProducts();
    } catch (e) {
      throw Exception('Failed to get low stock products: $e');
    }
  }

  /// Get inventory summary
  Future<Map<String, dynamic>> getInventorySummary() async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to view inventory summary');
    }

    try {
      final lowStockProducts = await getLowStockProducts();
      final activeAlerts = await getActiveStockAlerts();
      
      // Get recent transactions (last 7 days)
      final recentTransactions = await getAllTransactions(
        limit: 100,
        fromDate: DateTime.now().subtract(const Duration(days: 7)),
      );

      // Calculate summary statistics
      final totalProducts = await _getTotalProductCount();
      final outOfStockCount = lowStockProducts.where((p) => p.stock <= 0).length;
      final lowStockCount = lowStockProducts.where((p) => p.stock > 0).length;

      return {
        'totalProducts': totalProducts,
        'outOfStockCount': outOfStockCount,
        'lowStockCount': lowStockCount,
        'activeAlertsCount': activeAlerts.length,
        'recentTransactionsCount': recentTransactions.length,
        'lowStockProducts': lowStockProducts,
        'activeAlerts': activeAlerts,
        'recentTransactions': recentTransactions.take(10).toList(),
      };
    } catch (e) {
      throw Exception('Failed to get inventory summary: $e');
    }
  }

  /// Get total product count
  Future<int> _getTotalProductCount() async {
    try {
      // This would need to be implemented in ProductRepository
      final products = await _productRepository.getAllProducts(limit: 1000);
      return products.length;
    } catch (e) {
      return 0;
    }
  }

  /// Bulk update stock levels from CSV or similar data
  Future<Map<String, dynamic>> bulkUpdateStock(
    List<Map<String, dynamic>> stockUpdates,
  ) async {
    if (!canManageInventory) {
      throw Exception('Insufficient permissions to bulk update stock');
    }

    int successCount = 0;
    int failureCount = 0;
    List<String> errors = [];

    for (final update in stockUpdates) {
      try {
        final sku = update['sku']?.toString();
        final newStock = double.tryParse(update['stock']?.toString() ?? '0');
        final notes = update['notes']?.toString();

        if (sku == null || newStock == null) {
          errors.add('Invalid data for SKU: $sku');
          failureCount++;
          continue;
        }

        await updateStockBySku(
          sku: sku,
          newStock: newStock,
          notes: notes ?? 'Bulk update',
        );

        successCount++;
      } catch (e) {
        errors.add('Failed to update ${update['sku']}: $e');
        failureCount++;
      }
    }

    return {
      'successCount': successCount,
      'failureCount': failureCount,
      'errors': errors,
    };
  }
}
