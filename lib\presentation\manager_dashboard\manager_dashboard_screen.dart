import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/services/inventory_service.dart';
import '../../core/services/auth_service.dart';
import '../../core/models/product_model.dart';
import '../../core/models/stock_alert_model.dart';
import '../../core/models/inventory_transaction_model.dart';
import '../../theme/app_theme.dart';
import 'widgets/inventory_summary_card.dart';
import 'widgets/stock_alerts_widget.dart';
import 'widgets/low_stock_products_widget.dart';
import 'widgets/recent_transactions_widget.dart';
import 'inventory_management_screen.dart';
import 'stock_update_screen.dart';

class ManagerDashboardScreen extends StatefulWidget {
  const ManagerDashboardScreen({Key? key}) : super(key: key);

  @override
  State<ManagerDashboardScreen> createState() => _ManagerDashboardScreenState();
}

class _ManagerDashboardScreenState extends State<ManagerDashboardScreen> {
  final InventoryService _inventoryService = InventoryService.instance;
  final AuthService _authService = AuthService.instance;
  
  bool _isLoading = true;
  Map<String, dynamic>? _inventorySummary;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final summary = await _inventoryService.getInventorySummary();
      
      setState(() {
        _inventorySummary = summary;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = _authService.currentUser;
    
    if (user == null || !user.canManageInventory) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock, size: 16.w, color: Colors.grey),
              SizedBox(height: 2.h),
              Text(
                'You do not have permission to access this area.',
                style: TextStyle(fontSize: 14.sp),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Manager Dashboard'),
        backgroundColor: AppTheme.primaryLight,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'inventory':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const InventoryManagementScreen(),
                    ),
                  );
                  break;
                case 'stock_update':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const StockUpdateScreen(),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'inventory',
                child: Row(
                  children: [
                    Icon(Icons.inventory),
                    SizedBox(width: 2.w),
                    Text('Full Inventory'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'stock_update',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 2.w),
                    Text('Update Stock'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 16.w, color: Colors.red),
            SizedBox(height: 2.h),
            Text(
              'Error loading dashboard data',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 1.h),
            Text(
              _error!,
              style: const TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 2.h),
            ElevatedButton(
              onPressed: _loadDashboardData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_inventorySummary == null) {
      return const Center(
        child: Text('No data available'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome message
            Card(
              child: Padding(
                padding: EdgeInsets.all(4.w),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: AppTheme.primaryLight,
                      child: Icon(Icons.person, color: Colors.white),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome, ${_authService.currentUser?.fullName}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Manager Dashboard',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 2.h),

            // Inventory Summary Cards
            InventorySummaryCard(summary: _inventorySummary!),
            SizedBox(height: 2.h),

            // Stock Alerts
            if ((_inventorySummary!['activeAlerts'] as List).isNotEmpty) ...[
              StockAlertsWidget(
                alerts: (_inventorySummary!['activeAlerts'] as List)
                    .cast<StockAlertModel>(),
                onAlertResolved: _loadDashboardData,
              ),
              SizedBox(height: 2.h),
            ],

            // Low Stock Products
            if ((_inventorySummary!['lowStockProducts'] as List).isNotEmpty) ...[
              LowStockProductsWidget(
                products: (_inventorySummary!['lowStockProducts'] as List)
                    .cast<ProductModel>(),
                onStockUpdated: _loadDashboardData,
              ),
              SizedBox(height: 2.h),
            ],

            // Recent Transactions
            if ((_inventorySummary!['recentTransactions'] as List).isNotEmpty) ...[
              RecentTransactionsWidget(
                transactions: (_inventorySummary!['recentTransactions'] as List)
                    .cast<InventoryTransactionModel>(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
