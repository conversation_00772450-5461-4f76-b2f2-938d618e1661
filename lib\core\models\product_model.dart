class ProductModel {
  final String id;
  final String sku;
  final String name;
  final String description;
  final double price;
  final double? discountPrice;
  final String categoryId;
  final String? categoryName;
  final List<String> imageUrls;
  final String unit; // e.g., "kg", "piece", "liter"
  final double stock;
  final double minStockLevel;
  final double maxStockLevel;
  final bool isAvailable;
  final double? rating;
  final int? reviewCount;
  final Map<String, dynamic>? nutritionInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductModel({
    required this.id,
    required this.sku,
    required this.name,
    required this.description,
    required this.price,
    this.discountPrice,
    required this.categoryId,
    this.categoryName,
    this.imageUrls = const [],
    required this.unit,
    required this.stock,
    this.minStockLevel = 5.0,
    this.maxStockLevel = 100.0,
    this.isAvailable = true,
    this.rating,
    this.reviewCount,
    this.nutritionInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  double get finalPrice => discountPrice ?? price;
  bool get hasDiscount => discountPrice != null && discountPrice! < price;
  double get discountPercentage => hasDiscount ? ((price - discountPrice!) / price * 100) : 0;
  bool get inStock => stock > 0;
  bool get isLowStock => stock <= minStockLevel;
  bool get isOverstock => stock > maxStockLevel;
  String get primaryImageUrl => imageUrls.isNotEmpty ? imageUrls.first : '';
  String get stockStatus {
    if (stock <= 0) return 'Out of Stock';
    if (isLowStock) return 'Low Stock';
    if (isOverstock) return 'Overstock';
    return 'In Stock';
  }

  factory ProductModel.fromMap(Map<String, dynamic> map) {
    return ProductModel(
      id: map['id']?.toString() ?? '',
      sku: map['sku']?.toString() ?? '',
      name: map['name']?.toString() ?? '',
      description: map['description']?.toString() ?? '',
      price: double.tryParse(map['price']?.toString() ?? '0') ?? 0.0,
      discountPrice: map['discount_price'] != null
          ? double.tryParse(map['discount_price']?.toString() ?? '0')
          : null,
      categoryId: map['category_id']?.toString() ?? '',
      categoryName: map['category_name']?.toString(),
      imageUrls: map['image_urls'] is List
          ? List<String>.from(map['image_urls'])
          : (map['image_urls']?.toString().split(',') ?? []),
      unit: map['unit']?.toString() ?? 'piece',
      stock: double.tryParse(map['stock']?.toString() ?? '0') ?? 0.0,
      minStockLevel: double.tryParse(map['min_stock_level']?.toString() ?? '5') ?? 5.0,
      maxStockLevel: double.tryParse(map['max_stock_level']?.toString() ?? '100') ?? 100.0,
      isAvailable: map['is_available'] == true || map['is_available'] == 1,
      rating: map['rating'] != null 
          ? double.tryParse(map['rating']?.toString() ?? '0') 
          : null,
      reviewCount: map['review_count'] != null 
          ? int.tryParse(map['review_count']?.toString() ?? '0') 
          : null,
      nutritionInfo: map['nutrition_info'] is Map<String, dynamic> 
          ? map['nutrition_info'] 
          : null,
      createdAt: DateTime.parse(map['created_at']?.toString() ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at']?.toString() ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sku': sku,
      'name': name,
      'description': description,
      'price': price,
      'discount_price': discountPrice,
      'category_id': categoryId,
      'category_name': categoryName,
      'image_urls': imageUrls,
      'unit': unit,
      'stock': stock,
      'min_stock_level': minStockLevel,
      'max_stock_level': maxStockLevel,
      'is_available': isAvailable,
      'rating': rating,
      'review_count': reviewCount,
      'nutrition_info': nutritionInfo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ProductModel copyWith({
    String? id,
    String? sku,
    String? name,
    String? description,
    double? price,
    double? discountPrice,
    String? categoryId,
    String? categoryName,
    List<String>? imageUrls,
    String? unit,
    double? stock,
    double? minStockLevel,
    double? maxStockLevel,
    bool? isAvailable,
    double? rating,
    int? reviewCount,
    Map<String, dynamic>? nutritionInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProductModel(
      id: id ?? this.id,
      sku: sku ?? this.sku,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      discountPrice: discountPrice ?? this.discountPrice,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      imageUrls: imageUrls ?? this.imageUrls,
      unit: unit ?? this.unit,
      stock: stock ?? this.stock,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      maxStockLevel: maxStockLevel ?? this.maxStockLevel,
      isAvailable: isAvailable ?? this.isAvailable,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      nutritionInfo: nutritionInfo ?? this.nutritionInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, price: $finalPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
