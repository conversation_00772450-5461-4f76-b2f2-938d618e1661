import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class ProfileMenuWidget extends StatelessWidget {
  final dynamic userProfile; // UserProfile from user_profile_screen.dart
  final VoidCallback? onOrderHistory;
  final VoidCallback? onAddresses;
  final VoidCallback? onPaymentMethods;
  final VoidCallback? onNotifications;
  final VoidCallback? onManagerDashboard;
  final VoidCallback? onLogout;

  const ProfileMenuWidget({
    Key? key,
    required this.userProfile,
    this.onOrderHistory,
    this.onAddresses,
    this.onPaymentMethods,
    this.onNotifications,
    this.onManagerDashboard,
    this.onLogout,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<ProfileMenuItem> menuItems = [
      ProfileMenuItem(
        icon: Icons.history,
        title: 'Order History',
        subtitle: 'View all your orders',
        onTap: onOrderHistory,
        iconColor: Colors.blue,
      ),
      ProfileMenuItem(
        icon: Icons.location_on_outlined,
        title: 'Delivery Addresses',
        subtitle: 'Manage your addresses',
        onTap: onAddresses,
        iconColor: Colors.green,
      ),
      ProfileMenuItem(
        icon: Icons.payment_outlined,
        title: 'Payment Methods',
        subtitle: 'Manage payment options',
        onTap: onPaymentMethods,
        iconColor: Colors.purple,
      ),
      ProfileMenuItem(
        icon: Icons.notifications_outlined,
        title: 'Notifications',
        subtitle: 'Manage your notifications',
        onTap: onNotifications,
        iconColor: Colors.orange,
      ),
    ];

    final List<ProfileMenuItem> supportItems = [
      ProfileMenuItem(
        icon: Icons.help_outline,
        title: 'Help & Support',
        subtitle: 'Get help and support',
        onTap: () {},
        iconColor: Colors.teal,
      ),
      ProfileMenuItem(
        icon: Icons.info_outline,
        title: 'About',
        subtitle: 'App version and info',
        onTap: () {},
        iconColor: Colors.indigo,
      ),
    ];

    // Add manager dashboard item if user is a manager
    final List<ProfileMenuItem> managerItems = [];
    if (userProfile?.isManager == true) {
      managerItems.add(
        ProfileMenuItem(
          icon: Icons.dashboard,
          title: 'Manager Dashboard',
          subtitle: 'Manage inventory and stock',
          onTap: onManagerDashboard,
          iconColor: Colors.red,
        ),
      );
    }

    return Column(
      children: [
        // Manager section (only show if user is manager)
        if (managerItems.isNotEmpty) ...[
          _buildSectionTitle(context, 'Management'),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                for (int i = 0; i < managerItems.length; i++) ...[
                  _buildMenuItem(context, managerItems[i]),
                  if (i < managerItems.length - 1)
                    Divider(height: 1, color: Theme.of(context).dividerColor),
                ],
              ],
            ),
          ),
          SizedBox(height: 3.h),
        ],

        // Account section
        _buildSectionTitle(context, 'Account'),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              for (int i = 0; i < menuItems.length; i++) ...[
                _buildMenuItem(context, menuItems[i]),
                if (i < menuItems.length - 1)
                  Divider(height: 1, color: Theme.of(context).dividerColor),
              ],
            ],
          ),
        ),

        SizedBox(height: 3.h),

        // Support section
        _buildSectionTitle(context, 'Support'),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              for (int i = 0; i < supportItems.length; i++) ...[
                _buildMenuItem(context, supportItems[i]),
                if (i < supportItems.length - 1)
                  Divider(height: 1, color: Theme.of(context).dividerColor),
              ],
            ],
          ),
        ),

        SizedBox(height: 3.h),

        // Logout button
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          child: OutlinedButton.icon(
            onPressed: onLogout,
            icon: Icon(Icons.logout, color: Colors.red, size: 18.sp),
            label: const Text('Logout'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        SizedBox(height: 2.h),

        // App version
        Text(
          'Version 1.0.0',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(4.w, 0, 4.w, 2.h),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).textTheme.bodySmall?.color,
        ),
      ),
    );
  }

  Widget _buildMenuItem(BuildContext context, ProfileMenuItem item) {
    return InkWell(
      onTap: item.onTap,
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(2.5.w),
              decoration: BoxDecoration(
                color: item.iconColor.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(item.icon, color: item.iconColor, size: 20.sp),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    item.subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).textTheme.bodySmall?.color,
              size: 16.sp,
            ),
          ],
        ),
      ),
    );
  }
}

class ProfileMenuItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final Color iconColor;

  const ProfileMenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.onTap,
    required this.iconColor,
  });
}
